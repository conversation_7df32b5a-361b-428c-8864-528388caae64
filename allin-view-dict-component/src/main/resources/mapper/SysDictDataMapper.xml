<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allin.view.dict.mapper.SysDictDataMapper">
    <resultMap id="BaseResultMap" type="com.allin.view.dict.pojo.entity.SysDictData">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="projectId" column="project_id" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="label" column="label" jdbcType="VARCHAR"/>
        <result property="value" column="value" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="parentId" column="parent_id" jdbcType="BIGINT"/>
        <result property="isDisabled" column="is_disabled" jdbcType="TINYINT"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
    </resultMap>
</mapper>
