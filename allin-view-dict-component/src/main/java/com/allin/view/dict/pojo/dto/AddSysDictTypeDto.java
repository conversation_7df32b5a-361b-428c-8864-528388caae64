package com.allin.view.dict.pojo.dto;

import com.allin.view.base.enums.StatusEnums;
import com.allin.view.base.enums.validator.IEnumValid;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 系统字典类型
 *
 * <AUTHOR>
 */
@Data
public class AddSysDictTypeDto {

    /**
     * 字典名称
     */
    @NotBlank
    private String name;
    /**
     * 字典类型
     */
    @NotBlank
    private String type;
    /**
     * 状态，0禁用，1启用
     */
    @IEnumValid(target = StatusEnums.class, isNotNull = true)
    private Integer status;
    /**
     * 备注
     */
    private String remark;

}