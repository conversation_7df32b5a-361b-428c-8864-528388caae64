package com.allin.view.dict.manage;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.allin.view.config.filter.ThreadLocalClear;
import com.allin.view.dict.mapper.SysDictTypeMapper;
import com.allin.view.dict.pojo.vo.SysDictDataVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 字典管理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DictManager implements ThreadLocalClear {

    /**
     * 用来实现一次请求只查询一次的线程变量
     */
    public static final TransmittableThreadLocal<Map<String, List<SysDictDataVo>>> DICT_MAP = new TransmittableThreadLocal<>();

    private final SysDictTypeMapper sysDictTypeMapper = SpringUtil.getBean(SysDictTypeMapper.class);

    /**
     * 清除缓存
     */
    @Override
    public void clear() {
        DICT_MAP.remove();
    }

    /**
     * 根据项目id和字典类型获取字典数据
     */
    public List<SysDictDataVo> listDictByType(String type, String projectId) {
        String key = type + ":" + projectId;
        if (Objects.isNull(DICT_MAP.get())) {
            List<SysDictDataVo> result = sysDictTypeMapper.listDataByTypeAndProjectId(type, projectId);
            Map<String, List<SysDictDataVo>> map = new HashMap<>();
            map.put(key, result);
            DICT_MAP.set(map);
        } else {
            if (!DICT_MAP.get().containsKey(key)) {
                List<SysDictDataVo> result = sysDictTypeMapper.listDataByTypeAndProjectId(type, projectId);
                DICT_MAP.get().put(key, result);
            }
        }
        return DICT_MAP.get().get(key);
    }

    /**
     * 根据字典类型获取字典数据
     */
    public List<SysDictDataVo> listDictByType(String type) {
        if (Objects.isNull(DICT_MAP.get())) {
            List<SysDictDataVo> result = sysDictTypeMapper.listDataByType(type);
            Map<String, List<SysDictDataVo>> map = new HashMap<>();
            map.put(type, result);
            DICT_MAP.set(map);
        } else {
            if (!DICT_MAP.get().containsKey(type)) {
                List<SysDictDataVo> result = sysDictTypeMapper.listDataByType(type);
                DICT_MAP.get().put(type, result);
            }
        }
        return DICT_MAP.get().get(type);
    }

    /**
     * 根据字典类型和字典值列表，返回字典标签列表
     *
     * <AUTHOR>
     * @since 2025/4/24
     */
    public String valuesToString(String type, String values, String projectId) {
        final List<String> valueList = StrUtil.splitTrim(values, StrPool.COMMA);
        final List<SysDictDataVo> dictByType;
        if (StrUtil.isNotBlank(projectId)) {
            dictByType = listDictByType(type, projectId);
        } else {
            dictByType = listDictByType(type);
        }
        if (CollUtil.isEmpty(dictByType)) {
            return "-";
        }
        List<String> combinedLabel = new ArrayList<>();
        for (String valuePart : valueList) {
            for (SysDictDataVo sysDictDataVo : dictByType) {
                if (sysDictDataVo.getValue().equals(valuePart)) {
                    combinedLabel.add(sysDictDataVo.getLabel());
                    break;
                }
            }
        }
        return String.join(StrPool.COMMA, combinedLabel);
    }
}
