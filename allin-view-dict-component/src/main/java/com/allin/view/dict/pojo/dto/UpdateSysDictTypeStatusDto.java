package com.allin.view.dict.pojo.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.io.Serializable;

/**
 * 系统字典类型
 *
 * @TableName sys_dict_type
 */
@Data
public class UpdateSysDictTypeStatusDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @NotNull
    private String id;
    /**
     * 状态，0禁用，1启用
     */
    @Range(min = 0, max = 1, message = "取值范围:[0，1]")
    private Integer status;

}