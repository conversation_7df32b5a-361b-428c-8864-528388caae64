package com.allin.view.dict.service.impl;

import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.dict.mapper.SysDictDataMapper;
import com.allin.view.dict.mapper.SysDictTypeMapper;
import com.allin.view.dict.pojo.entity.SysDictData;
import com.allin.view.dict.pojo.entity.SysDictType;
import com.allin.view.dict.pojo.vo.SysDictDataVo;
import com.allin.view.dict.service.SysDictTypeService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 针对表【sys_dict_type(系统字典类型 )】的数据库操作Service实现
 */
@Service
public class SysDictTypeServiceImpl extends ServiceImpl<SysDictTypeMapper, SysDictType>
        implements SysDictTypeService {

    private final SysDictDataMapper sysDictDataMapper;

    public SysDictTypeServiceImpl(SysDictDataMapper sysDictDataMapper) {
        this.sysDictDataMapper = sysDictDataMapper;
    }

    @Override
    public SysDictType getByTypeAndName(String type, String name) {
        return baseMapper.selectOne(Wrappers.lambdaQuery(SysDictType.class)
                .nested(wrapper -> {
                    if (SecurityContextHolder.tryGetProjectId().isPresent()) {
                        wrapper.eq(SysDictType::getProjectId, SecurityContextHolder.getProjectId())
                                .or()
                                .isNull(SysDictType::getProjectId);
                    }
                })
                .eq(SysDictType::getType, type)
                .eq(SysDictType::getName, name));
    }

    @Override
    public List<SysDictDataVo> listDictDataByType(String type) {
        final Optional<String> projectId = SecurityContextHolder.tryGetProjectId();
        if (projectId.isPresent()){
            return baseMapper.listDataByTypeAndProjectId(type, projectId.get());
        }
        return baseMapper.listDataByType(type);
    }

    @Override
    public boolean removeAndDictDataById(String id) {
        final SysDictType dictType = getById(id);
        if (removeById(id)) {
            sysDictDataMapper.delete(Wrappers.lambdaQuery(SysDictData.class)
                    .eq(SysDictData::getType, dictType.getType()));
        }
        return true;
    }
}




