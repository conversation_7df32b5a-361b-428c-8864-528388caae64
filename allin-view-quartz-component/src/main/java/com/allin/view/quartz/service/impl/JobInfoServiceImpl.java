package com.allin.view.quartz.service.impl;

import com.allin.view.base.exception.service.ProgramException;
import com.allin.view.base.exception.service.ValidationFailureException;
import com.allin.view.quartz.enums.JobInfoStatusEnum;
import com.allin.view.quartz.manager.SchedulerManager;
import com.allin.view.quartz.mapper.JobInfoMapper;
import com.allin.view.quartz.pojo.dto.AddJobInfoDto;
import com.allin.view.quartz.pojo.entity.JobInfo;
import com.allin.view.quartz.pojo.vo.JobInfoVo;
import com.allin.view.quartz.service.JobInfoService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.quartz.SchedulerException;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * <p>
 * 定时任务表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-10
 */
@Service
public class JobInfoServiceImpl extends ServiceImpl<JobInfoMapper, JobInfo> implements JobInfoService {


    private final SchedulerManager schedulerManager;

    public JobInfoServiceImpl(SchedulerManager schedulerManager) {
        this.schedulerManager = schedulerManager;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addJob(AddJobInfoDto addJobInfoDto) throws SchedulerException {
        JobInfo jobInfo = getOne(Wrappers.lambdaQuery(JobInfo.class)
                .eq(JobInfo::getName, addJobInfoDto.getName()));
        if (Objects.nonNull(jobInfo)) {
            return updateJob(jobInfo);
        } else {
            final JobInfo newJobInfo = new JobInfo();
            BeanUtils.copyProperties(addJobInfoDto, newJobInfo);
            newJobInfo.setStatus(JobInfoStatusEnum.NORMAL.getStatus());
            if (save(newJobInfo)) {
                schedulerManager.addJob(newJobInfo);
                return true;
            }
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateJob(JobInfo jobInfo) throws SchedulerException {
        if (updateById(jobInfo)) {
            jobInfo = getById(jobInfo.getId());
            schedulerManager.updateJob(jobInfo);
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateJobStatus(String id, Integer status) throws SchedulerException {
        JobInfo jobInfo = JobInfo.builder()
                .id(id)
                .status(status)
                .build();
        if (updateById(jobInfo)) {
            if (JobInfoStatusEnum.NORMAL.getStatus().equals(status)) {
                schedulerManager.resumeJob(id);
            } else {
                schedulerManager.pauseJob(id);
            }
            return true;
        }
        return false;
    }

    @Override
    public void triggerJob(String id) throws SchedulerException {
        JobInfo jobInfo = getById(id);
        if (jobInfo != null) {
            schedulerManager.triggerJob(id, jobInfo.getHandlerName(), jobInfo.getHandlerParam());
        } else {
            throw new ValidationFailureException("不存在该ID的定时任务");
        }
    }

    @Override
    public void setRunInfo(JobInfoVo jobInfoVo) {
        final String id = jobInfoVo.getId();
        try {
            jobInfoVo.setState(schedulerManager.getStateStr(id));
            jobInfoVo.setNextFireTime(schedulerManager.getNextFireTimeStr(id));
            jobInfoVo.setPreviousFireTime(schedulerManager.getPreviousFireTimeStr(id));
        } catch (SchedulerException e) {
            throw new ProgramException("获取调度器运行相关信息失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeJobById(String id) throws SchedulerException {
        JobInfo jobInfo = getById(id);
        if (removeById(id)) {
            schedulerManager.deleteJob(jobInfo.getId(), jobInfo.getHandlerName());
            return true;
        }
        return false;
    }
}
