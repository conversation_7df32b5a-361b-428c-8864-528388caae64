<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allin.view.quartz.mapper.JobInfoMapper">
    <resultMap id="BaseResultMap" type="com.allin.view.quartz.pojo.entity.JobInfo">
        <!--@mbg.generated-->
        <!--@Table job_info-->
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="handler_name" jdbcType="VARCHAR" property="handlerName" />
        <result column="handler_param" jdbcType="VARCHAR" property="handlerParam" />
        <result column="cron_expression" jdbcType="VARCHAR" property="cronExpression" />
        <result column="retry_count" jdbcType="INTEGER" property="retryCount" />
        <result column="retry_interval" jdbcType="INTEGER" property="retryInterval" />
        <result column="monitor_timeout" jdbcType="INTEGER" property="monitorTimeout" />
        <result column="record_log" jdbcType="INTEGER" property="recordLog" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="updater" jdbcType="VARCHAR" property="updater" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>
</mapper>
