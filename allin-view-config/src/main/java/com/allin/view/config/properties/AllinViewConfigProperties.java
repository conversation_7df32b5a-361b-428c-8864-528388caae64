package com.allin.view.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * config 基础模块配置
 * <AUTHOR>
 * @date 2024/2/28
 */
@Data
@ConfigurationProperties(prefix = "allin.view.config")
public class AllinViewConfigProperties {

    /**
     * 启用全局异常拦截配置
     */
    private boolean exceptionEnable = true;

    /**
     * 启用redis配置
     */
    private boolean redisEnable = true;

    /**
     * restTemplate 连接默认超时时间
     */
    private int restConnectTimeout = 3000;

    /**
     * restTemplate 读取默认超时时间
     */
    private int restReadTimeout = 3000;
}
