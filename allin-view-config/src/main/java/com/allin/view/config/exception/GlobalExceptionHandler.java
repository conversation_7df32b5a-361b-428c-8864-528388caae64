package com.allin.view.config.exception;

import com.allin.view.base.domain.Result;
import com.allin.view.base.exception.BaseException;
import com.allin.view.base.exception.other.RemoteServiceException;
import com.allin.view.base.i18n.I18nUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.ConstraintViolationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * 全局异常处理器
 */
@ConditionalOnProperty(prefix = "allin.view.config", name = "exception-enabled", havingValue = "true", matchIfMissing = true)
@Order(Ordered.HIGHEST_PRECEDENCE)
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    public GlobalExceptionHandler() {
        log.info("全局异常处理器加载成功...");
    }

    /**
     * Validation 异常
     * 当使用实体类对象接收参数时候，校验失败触发该异常
     */
    @ExceptionHandler(value = {BindException.class, MethodArgumentNotValidException.class})
    public Result<String> handleBindingErrors(BindException e,
                                              HttpServletRequest request) {
        printErrorLog(e, request);
        final List<FieldError> allErrors = e.getFieldErrors();
        StringBuilder message = new StringBuilder();
        for (FieldError error : allErrors) {
            message.append(String.format("[%s]，%s", error.getField(), error.getDefaultMessage()));
            break;
        }
        return Result.fail(HttpStatus.BAD_REQUEST.value(), message.toString());
    }

    /**
     * Validation 异常
     * 当校验方法级别的入参时候，校验失败触发该异常
     */
    @ExceptionHandler(value = ConstraintViolationException.class)
    public Result<String> handleConstraintViolations(ConstraintViolationException e,
                                                     HttpServletRequest request) {
        printWarnLog(request.getRequestURI(), e.getMessage());
        StringBuilder message = new StringBuilder();
        // e.getMessage()的格式为"getUser.id: id不能为空, getUser.name: name不能为空"
        String[] msgs = e.getMessage().split("[,，]");
        for (String msg : msgs) {
            String[] fieldAndMsg = msg.split("[:：]");
            if (fieldAndMsg.length < 2) {
                continue; // 跳过格式不正确的条目
            }
            String[] fieldParts = fieldAndMsg[0].split("\\.");
            String field;
            if (fieldParts.length < 2) {
                field = fieldParts[0].strip();
            } else {
                field = fieldParts[1].strip();
            }
            String fieldMessage = fieldAndMsg[1].strip();
            message.append(String.format("[%s]，%s", field, fieldMessage));
            break;
        }
        return Result.fail(HttpStatus.BAD_REQUEST.value(), message.toString());
    }

    /**
     * 注解{@link RequestParam},参数必填异常
     */
    @ExceptionHandler(value = MissingServletRequestParameterException.class)
    public Result<String> handleMissingRequestParameters(MissingServletRequestParameterException e,
                                                         HttpServletRequest request) {
        printWarnLog(request.getRequestURI(), e.getMessage());
        String message = String.format("%s，%s", e.getParameterName(), I18nUtil.isEmpty());
        return Result.fail(HttpStatus.BAD_REQUEST.value(), message);
    }

    /**
     * 参数验证类异常
     */
    @ExceptionHandler(value = MethodArgumentTypeMismatchException.class)
    public Result<String> argumentExceptionHandler(final MethodArgumentTypeMismatchException e,
                                                   HttpServletRequest request) {
        printWarnLog(request.getRequestURI(), e.getMessage());
        String paramName = e.getName();
        String errorMessage;
        // 根据目标类型提供友好的错误信息
        Class<?> requiredType = e.getRequiredType();
        if (requiredType != null) {
            if (LocalDate.class.isAssignableFrom(requiredType)) {
                errorMessage = String.format("参数'%s'格式错误，请使用 yyyy-MM-dd 格式", paramName);
            } else if (LocalDateTime.class.isAssignableFrom(requiredType)) {
                errorMessage = String.format("参数'%s'格式错误，请使用 yyyy-MM-dd HH:mm:ss 格式", paramName);
            } else if (LocalTime.class.isAssignableFrom(requiredType)) {
                errorMessage = String.format("参数'%s'格式错误，请使用 HH:mm:ss 格式", paramName);
            } else {
                errorMessage = String.format("参数'%s'类型错误: %s", paramName, e.getMessage());
            }
        } else {
            errorMessage = String.format("参数'%s'类型错误", paramName);
        }
        return Result.fail(HttpStatus.BAD_REQUEST.value(), errorMessage);
    }

    /**
     * 请求方式不支持
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public Result<String> handleUnsupportedRequestMethod(HttpRequestMethodNotSupportedException e,
                                                         HttpServletRequest request) {
        printWarnLog(request.getRequestURI(), "请求方式不支持");
        return Result.fail(HttpStatus.BAD_REQUEST.value(), e.getMessage());
    }

    /**
     * 自定义基础异常
     */
    @ExceptionHandler(value = BaseException.class)
    public Result<String> handleBaseExceptions(BaseException e, HttpServletRequest request) {
        // 4xx Client Error
        if (e.getCode() >= 400 && e.getCode() < 500) {
            printWarnLog(request.getRequestURI(), e.getMessage());
        } else {
            printErrorLog(e, request);
        }
        if (e instanceof RemoteServiceException) {
            return Result.fail(e.getCode(), I18nUtil.getMessage("base.service.request.exception"));
        }
        return Result.fail(e.getCode(), e.getMessage());
    }

    /**
     * 兜底处理其他异常信息
     */
    @ExceptionHandler(value = Exception.class)
    public Result<String> exceptionHandler(final Exception e, HttpServletRequest request, HttpServletResponse response) {
        if (e instanceof NoResourceFoundException) {
            printWarnLog(request.getRequestURI(), "请求资源路径不存在");
            return Result.fail(HttpStatus.BAD_REQUEST.value(), e.getMessage());
        }
        printErrorLog(e, request);
        // 只清除缓存区的内容
        response.resetBuffer();
        return Result.fail(500, I18nUtil.getMessage("base.system.fail"));
    }

    /**
     * 输出error日志
     */
    private void printErrorLog(Exception e, HttpServletRequest request) {
        if (log.isErrorEnabled()) {
            log.error(String.format("请求方式: %s, 请求路径:%s", request.getMethod(), request.getRequestURI()), e);
        }
    }

    /**
     * 输出warn日志，但不打印具体堆栈信息
     */
    private void printWarnLog(String requestUrl, String msg) {
        if (log.isWarnEnabled()) {
            log.warn("请求路径:{} - {}", requestUrl, msg);
        }
    }
}
