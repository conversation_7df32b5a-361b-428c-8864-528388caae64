package com.allin.view.config.thread;

import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 异步线程池配置, 用来支撑 @Async 注解
 *
 * <AUTHOR>
 */
@EnableAsync
@Configuration
public class AsyncThreadPoolConfig implements AsyncConfigurer {

    private final ObjectProvider<AsyncUncaughtExceptionHandler> exceptionHandlerProvider;

    public AsyncThreadPoolConfig(ObjectProvider<AsyncUncaughtExceptionHandler> exceptionHandlerProvider) {
        this.exceptionHandlerProvider = exceptionHandlerProvider;
    }

    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return exceptionHandlerProvider.getIfUnique();
    }

    @Override
    public Executor getAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix("CustomAsync-");
        // 最大可创建的线程数
        executor.setMaxPoolSize(50);
        // 核心线程池大小
        executor.setCorePoolSize(10);
        // 队列最大长度
        executor.setQueueCapacity(1000);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
}
