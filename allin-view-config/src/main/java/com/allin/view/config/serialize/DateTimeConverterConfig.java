package com.allin.view.config.serialize;

import cn.hutool.core.util.StrUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.converter.Converter;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;

/**
 * java8 LocalDate,LocalDateTime, LocalTime传参支持
 * RequestParam 时间处理 <br/>
 * 注意：该语法不能使用lambda语法来写:<a href="https://github.com/spring-projects/spring-framework/issues/22509">Lambda Converter not support</a>
 *
 * <AUTHOR>
 * @date 2023/6/30
 */
@Configuration
public class DateTimeConverterConfig {

    /**
     * 默认日期时间格式
     */
    private static final DateTimeFormatter DATE_TIME_PATTERN_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 默认日期格式
     */
    private static final DateTimeFormatter DATE_PATTERN_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 默认时间格式
     */
    private static final DateTimeFormatter TIME_PATTERN_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");

    /**
     * 默认年月格式
     */
    private static final DateTimeFormatter YEAR_MONTH_PATTERN_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM");

    /**
     * LocalDateTime转换器，用于转换RequestParam和PathVariable参数
     */
    @Bean
    public Converter<String, LocalDateTime> localDateTimeConverter() {
        return new Converter<String, LocalDateTime>() {
            @Override
            public LocalDateTime convert(String source) {
                if (StrUtil.isBlank(source)) {
                    return null;
                }
                return LocalDateTime.parse(source, DATE_TIME_PATTERN_FORMATTER);
            }
        };
    }

    /**
     * LocalDate转换器，用于转换RequestParam和PathVariable参数
     */
    @Bean
    public Converter<String, LocalDate> localDateConverter() {
        return new Converter<String, LocalDate>() {
            @Override
            public LocalDate convert(String source) {
                if (StrUtil.isBlank(source)) {
                    return null;
                }
                return LocalDate.parse(source, DATE_PATTERN_FORMATTER);
            }
        };
    }

    /**
     * YearMonth转换器，用于转换RequestParam和PathVariable参数
     */
    @Bean
    public Converter<String, YearMonth> yearMonthConverter() {
        return new Converter<String, YearMonth>() {
            @Override
            public YearMonth convert(String source) {
                if (StrUtil.isBlank(source)) {
                    return null;
                }
                return YearMonth.parse(source, YEAR_MONTH_PATTERN_FORMATTER);
            }
        };
    }

    /**
     * LocalTime转换器，用于转换RequestParam和PathVariable参数
     */
    @Bean
    public Converter<String, LocalTime> localTimeConverter() {
        return new Converter<String, LocalTime>() {
            @Override
            public LocalTime convert(String source) {
                if (StrUtil.isBlank(source)) {
                    return null;
                }
                return LocalTime.parse(source, TIME_PATTERN_FORMATTER);
            }
        };
    }
}
