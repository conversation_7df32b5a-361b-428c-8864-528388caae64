<!doctype html>
<html lang="en" style="height: 100%; width:100%; padding: 0; margin: 0;">
<head>
    <title>复合组件-单线程版本</title>
    <!-- Required meta tags -->
    <meta charset="utf-8"/>
    <meta
            name="viewport"
            content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />
    <style>
        #video101 {
            pointer-events: none; /* 禁用所有用户交互 */
        }

        #video101::-webkit-media-controls {
            display: none !important; /* 隐藏 WebKit 浏览器的媒体控件 */
        }

        #video101::-moz-media-controls {
            display: none !important; /* 隐藏 Firefox 的媒体控件 */
        }

        .aspect-ratio-16-9 {
            aspect-ratio: 16/9;
        }
    </style>
</head>
<body style="height: 100%; width:100%; padding: 0; margin: 0; overflow: hidden;background-color: rgb(2, 19, 44);display: flex; justify-content: center;">
<div style="height:100%; overflow: hidden; " id="video101Wrapper">
    <video id="video101" playsinline="" controls autoplay="" muted="" width="100%" height="100%"
           style="object-fit: fill;"></video>
</div>
<!-- Optional JavaScript -->
<!-- jQuery first, then Popper.js, then Bootstrap JS -->
<script
        src="../media/jquery-3.3.1.slim.min.js"
        integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo"
        crossorigin="anonymous"
></script>

<script>
    document.getElementById('video101').addEventListener('canplaythrough', function (e) {
        window.parent.postMessage('canplay', '*');
    })
    document.getElementById('video101').addEventListener('canplay', function (e) {
        window.parent.postMessage('canplay', '*');
    })
    document.getElementById('video101').addEventListener('loadeddata', function (e) {
        window.parent.postMessage('canplay', '*');
    })

    let contextPath = ''
    let ayPlayer
    let token = ''
    let timeoutId = null;  // 存储定时器ID
    // 协议类型 https(基于quic) 或者 wss(基于tcp)
    let protocolType = ''
    $(async function () {
        if (isChrome()) {
            await loadScript('./MT/AyPlayerMain-min.js');
        } else {
            // 如果是 Chrome 浏览器，加载对应的 JS 文件
            await loadScript('AyPlayer-min.js');
        }

        window.addEventListener('beforeunload', (event) => {
            // 在这里执行您的清理代码
            console.warn('卸载视频流..')
            closePlayer()
        });

        console.log("page script starting...");
        let cameraInfo = getQueryVariable('cameraInfo')
        if (!cameraInfo) {
            console.error('cameraInfo is null')
            return
        }

        // 是否保持16/9
        let isAspectRatio = getQueryVariable('isAspectRatio')
        setAspectRatio(isAspectRatio && Number(isAspectRatio) === 1)

        console.log(cameraInfo)
        contextPath = getQueryVariable('baseURL')
        token = getQueryVariable('token')
        protocolType = getQueryVariable('protocolType') ? getQueryVariable('protocolType') : 'https'
        await playByCamera(JSON.parse(decodeURI(cameraInfo)))

        window.addEventListener('message', (event) => {
            if (event.data === 'enterFullscreen') {
                console.log('进入全屏..使用contain模式');
                const video = document.getElementById('video101');
                if (video) {
                    video.style.objectFit = 'contain';
                }
            } else if (event.data === 'exitFullscreen') {
                console.log('不是全屏..使用fill模式');
                const video = document.getElementById('video101');
                if (video) {
                    video.style.objectFit = 'fill';
                }
            }
        });

    })


    /**
     * 是否保持16/9
     */
    function setAspectRatio(flag) {
        flag && $("#video101Wrapper").addClass("aspect-ratio-16-9");
        !flag && $("#video101Wrapper").css("width", '100%');
    }

    function loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.crossOrigin = 'anonymous';
            script.onload = () => resolve(script);
            script.onerror = () => reject(new Error(`脚本加载失败: ${src}`));
            document.head.appendChild(script);
        });
    }

    /**
     * 判断是否是chorme浏览器
     * @return {boolean}
     */
    function isChrome() {
        const userAgent = navigator.userAgent.toLowerCase();
        return /chrome/.test(userAgent) && !/edg|opr|brave/.test(userAgent);
    }

    /**
     * 关闭播放器
     */
    function closePlayer() {
        if (ayPlayer) {
            ayPlayer.closePlayer()
        }
    }

    /**
     * 保持播放器活跃
     */
    async function keepAlive(cameraId) {
        let video = document.getElementById('video101')
        if (
            video &&
            video.currentTime > 0 &&
            !video.paused &&
            !video.ended &&
            video.readyState > 2
        ) {
            console.log('keepAlive...')
            if (cameraId === false) {
                console.error('cameraId is empty')
                return
            }
            let data = await fetch(
                `${contextPath}/media/keepAlive?cameraId=${cameraId}`,
                {
                    method: 'POST',
                    headers: {
                        Token: token
                    }
                }
            )
            let result = await data.json()
            if (result.code === 200) {
                console.log('keepAlive success')
            } else {
                console.error('keepAlive failed')
            }
        } else {
            console.warn('video not ready...')
        }
        setTimeout(() => {
            keepAlive(cameraId)
        }, 1000 * 10)
    }


    /**
     * 视频流保活
     */
    function keepAliveMediaStream(devId) {
        let video = document.getElementById('video101')
        if (
            !(video &&
                video.currentTime > 0 &&
                !video.paused &&
                !video.ended &&
                video.readyState > 2)
        ) {
            console.warn('restart stream.. =========>', devId)
            stopStream()
            startStream(devId)
        }
        setTimeout(() => {
            keepAliveMediaStream(devId)
        }, 5000)
    }


    /**
     * 停止播放
     */
    function stopStream() {
        if (ayPlayer) {
            try {
                ayPlayer.stopStream()
            } catch (err) {
                console.error('stop failed. err=%o', err);
            }
        }
    }

    /**
     * 初始化播放器
     */
    async function initPlayer(serverIp, webPort) {
        console.log('function initPlayer starting.')
        if (!serverIp) {
            console.error('serverIp is empty')
            return
        }
        if (!webPort) {
            console.error('webPort is empty')
            return
        }
        const url = `${protocolType}://${serverIp}:${webPort}/`
        if (ayPlayer) {
            stopStream()
        }
        console.log('================>', url)
        if (isChrome()) {
            console.log('是chrome浏览器')
            ayPlayer = new AyPlayerMain("video101", 0, 5, 'mse');
        } else {
            console.log('不是chrome浏览器')
            ayPlayer = new AyPlayer('video101', 0, 0, 'mse')
        }
        //初始化视频源，并启动工作线程
        try {
            await ayPlayer.initPlayer(url)
        } catch (err) {
            console.error('await ayPlayer.initAndStartReceiving failed. err=%o', err);
            window.parent.postMessage('playError', '*');
        }
        console.log('function initPlayer finished.')
    }

    /**
     * 开始播放
     */
    function startStream(streamId) {
        if (ayPlayer.isReady()) {
            ayPlayer.startStream(streamId);
            if (timeoutId !== null) {
                clearTimeout(timeoutId);  // 清除之前的定时器
            }
            timeoutId = null;  // 重置定时器ID
        } else {
            // 清除并重新设置定时器，直到播放器准备好
            if (timeoutId !== null) {
                clearTimeout(timeoutId);  // 清除之前的定时器
            }
            timeoutId = setTimeout(() => {
                startStream(streamId);  // 10ms 后再次尝试
            }, 10);
        }
    }

    /**
     * 获取url中的参数
     * @param {Object} variable key
     */
    function getQueryVariable(variable) {
        let query = window.location.search.substring(1)
        let vars = query.split('&')
        for (let i = 0; i < vars.length; i++) {
            let pair = vars[i].split('=')
            if (pair[0] === variable) {
                return decodeURI(pair[1])
            }
        }
        return false
    }

    /**
     * 播放视频
     * @param cameraInfo
     */
    async function playByCamera(cameraInfo) {
        let {
            cameraId, // 相机ID
            cameraIp, // 相机IP
            userName, // 相机用户名
            password, // 相机密码
            channel, // 通道
            stream, // 码流
            startTime, // 开始时间 (直播可不传)
            endTime, // 结束时间 (直播可不传)
            mediaServerIp, // 流媒体服务器IP
            mediaServerRestApiPort, // 流媒体服务器RestApi端口
            mediaServerWebPort, // 流媒体服务器Web端口
            rtsp, // 直接RTSP ID
            devId // 播放通道ID
        } = cameraInfo
        // 0. 直接播放某个通道的情况
        if (devId && typeof devId === 'number' && devId > 0) {
            await startPlayer(mediaServerIp, mediaServerWebPort, devId, cameraId)
            return
        }
        // 1、添加视频通道
        await addDevice(
            cameraIp,
            cameraId,
            userName,
            decodeURIComponent(password),
            channel,
            stream,
            startTime,
            endTime,
            mediaServerIp,
            mediaServerRestApiPort,
            mediaServerWebPort,
            rtsp
        )
    }

    /**
     * 增加设备
     */
    async function addDevice(
        cameraIp,
        cameraId,
        userName,
        password,
        channel,
        stream,
        startTime,
        endTime,
        mediaServerIp,
        mediaServerRestApiPort,
        mediaServerWebPort,
        rtsp
    ) {
        // 判断是否为空
        if (!cameraId) {
            alert('请输入相机id')
            return
        }
        if (!cameraIp) {
            alert('请输入相机ip')
            return
        }
        if (!userName) {
            alert('请输入用户名')
            return
        }
        if (!password) {
            alert('请输入密码')
            return
        }
        if (!channel) {
            alert('请输入通道号')
            return
        }
        if (!stream) {
            alert('请输入码流')
            return
        }
        if (!mediaServerIp) {
            alert('请输入流媒体服务IP')
            return
        }
        if (!mediaServerRestApiPort) {
            alert('请输入流媒体服务rest端口')
            return
        }
        let data = {
            mediaServerRestApiPort: mediaServerRestApiPort,
            mediaServerIp: mediaServerIp,
            cameraId: cameraId,
            cameraIp: cameraIp,
            userName: userName,
            password: password,
            channel: channel,
            stream: stream,
            startTime: startTime ? startTime : null,
            endTime: endTime ? endTime : null,
            rtsp: rtsp ? rtsp : null
        }
        let res = await fetch(`${contextPath}/media/play`, {
            method: 'POST',
            headers: {
                Token: token,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })
        let resData = await res.json()
        if (resData.code === 200) {
            let devId = resData.data['devId']
            await startPlayer(mediaServerIp, mediaServerWebPort, devId, cameraId)
        } else {
            window.parent.postMessage('addDeviceError', '*');
            console.error('添加失败' + resData.message)
        }
    }

    /**
     * 开始直播视频
     */
    async function startPlayer(mediaServerIp, mediaServerWebPort, devId, cameraId) {
        await initPlayer(mediaServerIp, mediaServerWebPort)
        startStream(devId)
        keepAliveMediaStream(devId)
        if (cameraId) {
            await keepAlive(cameraId)
        }
    }
</script>
</body>
</html>
