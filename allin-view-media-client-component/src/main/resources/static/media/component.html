<!doctype html>
<html lang="en" style="height: 100%">
<head>
    <title>组件供引用</title>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
</head>
<body style="height: 100%;padding: 0;margin: 0;overflow: hidden">
<video id="video101" playsinline="" controls="" autoplay="" muted="" width="100%" height="100%"
       style="object-fit: fill;"></video>
<!-- Optional JavaScript -->
<!-- jQuery first, then Popper.js, then Bootstrap JS -->
<script src="../media/jquery-3.3.1.slim.min.js"
        integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo"
        crossorigin="anonymous"></script>
<script>
    let ayPlayer;
    $(async function () {
        if (isChrome()) {
            await loadScript('./MT/AyPlayerMain-min.js');
        } else {
            // 如果是 Chrome 浏览器，加载对应的 JS 文件
            await loadScript('AyPlayer-min.js');
        }

        window.addEventListener('beforeunload', (event) => {
            // 在这里执行您的清理代码
            console.warn('卸载视频流..')
            closePlayer()
        });
        await initPlayer()
        startStream(getQueryVariable('devId'));
    })

    function closePlayer() {
        if (ayPlayer) {
            ayPlayer.closePlayer()
        }
    }

    /**
     * 获取url中的参数
     * @param {Object} variable key
     */
    function getQueryVariable(variable) {
        let query = window.location.search.substring(1);
        let vars = query.split('&');
        for (let i = 0; i < vars.length; i++) {
            let pair = vars[i].split('=');
            if (pair[0] === variable) {
                return decodeURI(pair[1]);
            }
        }
        return false;
    }

    function stopStream() {
        if (ayPlayer) {
            ayPlayer.stopStream();
        }
    }

    /**
     * 判断是否是chorme浏览器
     * @return {boolean}
     */
    function isChrome() {
        const userAgent = navigator.userAgent.toLowerCase();
        return /chrome/.test(userAgent) && !/edg|opr|brave/.test(userAgent);
    }

    function loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.crossOrigin = 'anonymous';
            script.onload = () => resolve(script);
            script.onerror = () => reject(new Error(`脚本加载失败: ${src}`));
            document.head.appendChild(script);
        });
    }

    async function initPlayer() {
        console.log('function initPlayer starting.');
        let serverIp = getQueryVariable('serverIp');
        let webPort = getQueryVariable('webPort');
        const url = `ws://${serverIp}:${webPort}/`
        if (ayPlayer) {
            stopStream()
        }
        console.log('================>', url)
        if (isChrome()) {
            console.log('是chrome浏览器')
            ayPlayer = new AyPlayerMain("video101", 0, 5, 'mse');
        } else {
            console.log('不是chrome浏览器')
            ayPlayer = new AyPlayer('video101', 0, 0, 'mse')
        }
        //初始化视频源，并启动工作线程
        try {
            await ayPlayer.initPlayer(url)
        } catch (err) {
            console.error('await ayPlayer.initAndStartReceiving failed. err=%o', err);
            window.parent.postMessage('playError', '*');
        }
        console.log('function initPlayer finished.')
    }

    function startStream(streamId) {
        if (ayPlayer.isReady()) {
            ayPlayer.startStream(streamId);
        } else {
            setTimeout(() => {
                startStream(streamId);
            }, 1000);
        }
    }
</script>
</body>
</html>