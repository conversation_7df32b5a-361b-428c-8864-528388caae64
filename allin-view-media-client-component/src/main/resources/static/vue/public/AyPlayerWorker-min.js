const START_CODE=65297;class AyPlayerWorker{#t=0;#i=null;#h=null;#o=null;#l=null;#u=false;#m=null;#p=null;#D=false;#k=null;#R=null;#A=null;#T=null;#W=null;#S=null;#g=null;#v=null;#V=null;#C=null;#P=null;#O=null;constructor(t){if(t){this.#t=t}}async initAndStartReceiving(t){if(t.startsWith("wss://")){this.#D=true;this.#U(t);return}this.#i=new WebTransport(t);this.#i.closed.then(()=>{this.#u=false;self.postMessage({type:"message",isReady:false})}).catch(t=>{this.#u=false;self.postMessage({type:"message",isReady:false})});try{await this.#i.ready}catch(t){this.#u=false;self.postMessage({type:"message",isReady:false});return}this.#u=true;try{const e=await this.#i.createBidirectionalStream();this.#h=e.writable.getWriter();this.#o=e.readable.getReader()}catch(t){this.#u=false;self.postMessage({type:"message",isReady:false});return}self.postMessage({type:"message",isReady:true});this.#O=0;this.#W=0;this.#B()}startStream(t){this.#l=t;this.#_(1)}stopStream(){if(this.#l){this.#_(2)}}closeTransport(){this.#i.close()}isReady(){return this.#u}#_(t){if(!this.#u){return}const e=new Uint8Array(12);const s=new DataView(e.buffer);s.setUint32(0,START_CODE);s.setUint32(4,t);s.setUint32(8,this.#l);if(this.#D){this.#i.send(e)}else{this.#h.write(e)}}async#B(){while(true){try{let{done:t,value:e}=await this.#o.read();this.#O=this.#O+e.length;if(t){this.#i.close();return}if(this.#m){this.#m=new Uint8Array([...this.#m,...e])}else{this.#m=e}}catch(t){return}if(this.#m.length<12){continue}const t=await this.#F();if(!t){this.#i.close();return}}}async#F(){if(this.#t==0){}while(true){const s=new DataView(this.#m.buffer);const t=s.getUint32(0);const e=s.getUint32(4);const i=s.getUint32(8);if(t!=START_CODE||e>16||i>4096e3){this.#i.close();return false}if(this.#m.length<i+12){if(this.#t>=0){}return true}if(e==1){if(this.#W==1){return true}this.#R=s.getUint32(12);this.#A=s.getUint32(16);const a=new DataView(this.#m.buffer,20,i-8);const r=new TextDecoder;this.#T=r.decode(a);if(this.#k){this.#k.close()}this.#W=1;await this.#H()}else if(e==2){if(this.#p===null){this.#p=s.getBigUint64(16)}const n=s.getUint32(12);const h=s.getBigUint64(16)-this.#p;const o=s.getUint32(24);const l=s.getBigUint64(16);if(this.#t==0){}const c=new DataView(this.#m.buffer,28,i-16);let t=null;if(n==1){t="key"}else{t="delta"}let e=Date.now()-this.#P;if(e>60||e<20){}this.#P=Date.now();const d=new EncodedVideoChunk({type:t,timestamp:h.toString(),duration:o,data:c});this.#k.decode(d)}else if(e==3){const f=s.getUint32(12);const a=new DataView(this.#m.buffer,16,i-4);const r=new TextDecoder;const u=r.decode(a)}else if(e==4){this.#g=s.getUint32(12);this.#v=s.getUint32(16);const a=new DataView(this.#m.buffer,20,i-8);const r=new TextDecoder;this.#V=r.decode(a);if(this.#S){this.#S.close()}this.#I()}else if(e==5){if(this.#p===null){this.#p=s.getBigUint64(16)}const y=s.getUint32(12);const h=s.getBigUint64(16)-this.#p;const o=s.getUint32(24);if(this.#t==0){}const c=new DataView(this.#m.buffer,28,i-16);let t=null;if(y==1){t="key"}else{t="delta"}const d=new EncodedAudioChunk({type:t,timestamp:h.toString(),duration:o,data:c});this.#S.decode(d)}this.#m=this.#m.slice(i+12);if(this.#m.length<12){if(this.#t==0){}return true}}if(this.#t==0){}}async#M(t){let e=Date.now()-this.#C;if(e>50||e<30){}this.#C=Date.now();self.postMessage({type:"video",content:t},[t])}async#j(t){self.postMessage({type:"audio",content:t},[t])}async#H(){const t={output:this.#M.bind(this),error:t=>{}};const e={codec:this.#T,codedWidth:this.#R,codedHeight:this.#A,hardwareAcceleration:"prefer-hardware"};const s=await VideoDecoder.isConfigSupported(e);if(!s.supported){e.hardwareAcceleration="prefer-software"}this.#k=new VideoDecoder(t);this.#k.configure(e);this.#C=0;this.#P=0;this.#p=null;this.#W=2}#I(){const t={output:this.#j.bind(this),error:t=>{}};const e={codec:this.#V,numberOfChannels:this.#g,sampleRate:this.#v};this.#S=new AudioDecoder(t);this.#S.configure(e)}#U(t){this.#i=new WebSocket(t);this.#i.binaryType="arraybuffer";this.#i.onopen=async()=>{this.#u=true;self.postMessage({type:"message",isReady:true})};this.#i.onclose=async()=>{this.#u=false;self.postMessage({type:"message",isReady:false})};this.#i.onmessage=async e=>{if(e.data instanceof ArrayBuffer){let t=new Uint8Array(e.data);if(this.#m){this.#m=new Uint8Array([...this.#m,...t])}else{this.#m=t}const s=await this.#F();if(!s){this.#i.close()}}};this.#O=0}}var ayPlayerWorker;self.onmessage=t=>{const e=t.data;switch(e.type){case"initWorker":ayPlayerWorker=new AyPlayerWorker(e.clientId);try{ayPlayerWorker.initAndStartReceiving(e.serverUrl)}catch(t){}break;case"startStream":ayPlayerWorker.startStream(e.streamId);break;case"stopStream":ayPlayerWorker.stopStream();break;case"closeWorker":ayPlayerWorker.closeTransport();self.close();break}};