import { createRouter, createWebHistory } from 'vue-router'
import TestView from "@/components/TestView.vue";
import TestView2 from "@/components/TestView2.vue";
import TestView3 from "@/components/TestView3.vue";
import TestView4 from "@/components/TestView4.vue";
import TestView5 from "@/components/TestView5.vue";
const routes = [
  {
    path: '/',
    name: 'test',
    component: TestView
  },{
    path: '/test2',
    name: 'test2',
    component: TestView2
  },{
    path: '/test3/:videoCount',
    name: 'test3',
    component: TestView3,
    props: true
  },{
    path: '/test4',
    name: 'test4',
    component: TestView4,
    props: true
  },{
    path: '/test5',
    name: 'test5',
    component: TestView5,
    props: true
  },
  {
    path: '/about',
    name: 'about',
    // route level code-splitting
    // this generates a separate chunk (about.[hash].js) for this route
    // which is lazy-loaded when the route is visited.
    component: () => import(/* webpackChunkName: "about" */ '../views/AboutView.vue')
  }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})

export default router
