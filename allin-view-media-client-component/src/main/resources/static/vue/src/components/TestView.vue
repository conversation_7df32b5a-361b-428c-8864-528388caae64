<template>
  <div class="test1">
    <video id="video101" playsinline="" controls="" autoplay="" muted="" width="100%" height="100%"
           style="object-fit: fill;"></video>
  </div>
</template>

<script setup>
import {onMounted, onUnmounted} from "vue";
import AyPlayerMain from "./play/AyPlayerMain-min";
let ayPlayer;
function initPlayer() {
  console.log('function initPlayer starting.');
  const url = 'https://bird.allin-tech.com:9193/';
  ayPlayer = new AyPlayerMain("video101");
  //初始化视频源，并启动工作线程
  try {
    ayPlayer.initAndStartWorker(url);
  } catch (err) {
    console.error('ayPlayer.initAndStartWorker failed. err=%o', err);
  }
  console.log('function initPlayer finished.');
}

function startStream(streamId) {
  if (streamId) {
    ayPlayer.startStream(streamId);
  } else {
    ayPlayer.startStream(101);
  }
}

function stopStream() {
  ayPlayer.stopStream();
}

function closePlayer() {
  ayPlayer.closeWorker();
}

function displayTimestamp() {
  const videoElement = document.getElementById("video101");
  console.log(videoElement.currentTime);
}

function autoStartStream(streamId) {
  if (ayPlayer.isReady()) {
    ayPlayer.startStream(streamId);
  } else {
    setTimeout(autoStartStream, 10, streamId);
  }
}

onMounted(() => {
  initPlayer();
  const videoElement = document.getElementById("video101");
  autoStartStream(207);
});

onUnmounted(() => {
  closePlayer();
})
</script>


<style scoped>
.test1 {
  height: 100%;
}
</style>