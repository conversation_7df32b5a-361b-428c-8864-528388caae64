<template>
  <div class="test1">
    <PlayerFullComponent :cameraInfo="cameraInfo"/>
  </div>
</template>

<script setup>
import PlayerFullComponent from "@/components/PlayerFullComponent.vue";
import {reactive} from "vue";
let cameraInfo = reactive({
  cameraId: '************', // 相机ID
  cameraIp: '************', // 相机IP
  userName: 'admin', // 相机用户名
  password: 'Allin2018', // 相机密码
  channel: 1,  // 通道
  stream: 1,  // 码流
  startTime: '2024-03-27 10:26:00', // 开始时间 (直播可不传)
  endTime: '2024:03:27 15:24:00', // 结束时间 (直播可不传)
  mediaServerIp: 'bird.allin-tech.com', // 流媒体服务器IP
  mediaServerRestApiPort: 9191, // 流媒体服务器RestApi端口
  mediaServerWebPort: 9193 // 流媒体web端口
});
</script>


<style scoped>
.test1 {
  height: 100%;
  overflow: hidden;
}
</style>