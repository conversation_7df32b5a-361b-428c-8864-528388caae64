<template>
  <video :id="`video${videoIndex}`" playsinline="" controls="" autoplay="" muted="" width="100%" height="100%"
         style="object-fit: fill;"></video>
</template>

<script>
import {defineComponent, onMounted, onUnmounted, ref} from "vue";
import AyPlayerMain from "./play/AyPlayerMain-min";
export default defineComponent({
  props: {
    devId: {
      type: Number,
      required: true
    },
    videoIndex: {
      type: Number,
      required: true,
      default: 0
    }
  },
  setup(props) {
    let ayPlayer;
    function initPlayer() {
      console.log('function initPlayer starting.');
      const url = 'https://bird.allin-tech.com:9193/';
      ayPlayer = new AyPlayerMain(`video${props.videoIndex}`, new Date().getTime());
      //初始化视频源，并启动工作线程
      try {
        ayPlayer.initAndStartWorker(url);
      } catch (err) {
        console.error('ayPlayer.initAndStartWorker failed. err=%o', err);
      }
      console.log('function initPlayer finished.');
    }

    function closePlayer() {
      console.log('closePlayer starting.');
      ayPlayer.closeWorker();
    }

    function autoStartStream(streamId) {
      if (ayPlayer.isReady()) {
        ayPlayer.startStream(streamId);
      } else {
        setTimeout(autoStartStream, 10, streamId);
      }
    }

    onMounted(() => {
      console.log('setTimeout starting.');
      initPlayer();
      console.log(props.devId, props.videoIndex)
      autoStartStream(props.devId);
    });

    onUnmounted(() => {
      closePlayer();
    })

    return {
    }
  }
})


</script>


<style scoped>
.test1 {
  height: 100%;
}
</style>