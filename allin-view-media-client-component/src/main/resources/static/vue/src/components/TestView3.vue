<script setup>
import PlayerComponent from "@/components/PlayerComponent.vue";
import {onMounted, reactive, ref} from "vue";
import {useRoute} from "vue-router";
const devIds = new ref([102, 103, 203, 204, 205, 206, 102, 103, 203, 204, 205, 206,102, 103, 203, 204, 205, 206,102, 103, 203, 204, 205, 206]);
// const devIds = new ref([102, 103, 203]);
const route = useRoute();
console.log(route.params);
const videoCount = new ref();
onMounted(() => {
  videoCount.value = Number(route.params.videoCount);
})
console.log(videoCount);
</script>

<template>
  <div class="test1" v-if="videoCount">
    <div class="test2" v-for="(item, index) in videoCount" :key="index" >
      <PlayerComponent :dev-id="devIds[index]" :videoIndex="index"/>
    </div>
  </div>
</template>

<style scoped>
.test1 {
  height: 100%;
  overflow: auto;
  display: flex;
  flex-wrap: wrap;
  align-content: start;
  .test2 {
    width: 25%;
    height: 25%;
    border: 1px solid gold;
    box-sizing: border-box;
  }
}
</style>