<template>
  <video :id="`video${guidId}`" playsinline="" controls="" autoplay="" muted="" width="100%" height="100%"
         style="object-fit: fill;"></video>
</template>

<script>
import {defineComponent, onMounted, onUnmounted, ref} from "vue";
import AyPlayerMain from "./play/AyPlayerMain-min";
export default defineComponent({
  props: {
    cameraInfo: {
      type: Object,
      required: true
    },
  },
  setup(props) {
    let ayPlayer;
    let contextPath = '/api';
    let guidId = guid();
    function guid() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        let r = Math.random() * 16 | 0, v = c === 'x' ? r : (r&0x3 | 0x8);
        return v.toString(16);
      });
    }

    /**
     * 播放视频
     * @param cameraInfo
     */
    async function playByCamera(cameraInfo) {
      let {
        cameraId, // 相机ID
        cameraIp, // 相机IP
        userName, // 相机用户名
        password, // 相机密码
        channel,  // 通道
        stream,  // 码流
        startTime, // 开始时间 (直播可不传)
        endTime, // 结束时间 (直播可不传)
        mediaServerIp, // 流媒体服务器IP
        mediaServerRestApiPort, // 流媒体服务器RestApi端口
        mediaServerWebPort // 流媒体服务器Web端口
      } = cameraInfo;
      // 1. 判断cameraId对应的视频通道是否存在
      let isExist = await checkDeviceExist(mediaServerIp, mediaServerWebPort, cameraId)
      if (isExist) {
        return;
      }
      // 2、不存在则添加视频通道
      await addDevice(cameraIp, cameraId, userName, password, channel, stream, startTime, endTime, mediaServerIp, mediaServerRestApiPort, mediaServerWebPort);
    }

    /**
     * 检查设备是否存在
     * @param mediaServerIp
     * @param mediaServerWebPort
     * @param cameraId
     * @returns {Promise<boolean>}
     */
    async function checkDeviceExist(mediaServerIp, mediaServerWebPort, cameraId) {
      if (!cameraId) {
        console.error('请输入相机id');
        return false;
      }
      let params = new URLSearchParams();
      params.append('cameraId', cameraId);
      let res = await fetch(`${contextPath}/media/check/device/exist?${params.toString()}`, {method: 'GET'});
      let data = await res.json();
      if (data.code === 200) {
        let devId = data.data['devId']
        startPlayer(mediaServerIp, mediaServerWebPort, devId, cameraId);
        return true;
      } else {
        console.info(cameraId + '设备不存在');
      }
      return false;
    }

    /**
     * 增加设备
     */
    async function addDevice(cameraIp, cameraId, userName, password, channel, stream, startTime, endTime, mediaServerIp, mediaServerRestApiPort, mediaServerWebPort) {
      // 判断是否为空
      if (!cameraId) {
        console.error('请输入相机id');
        return;
      }
      if (!cameraIp) {
        console.error('请输入相机ip');
        return;
      }
      if (!userName) {
        console.error('请输入用户名');
        return;
      }
      if (!password) {
        console.error('请输入密码');
        return;
      }
      if (!channel) {
        console.error('请输入通道号');
        return;
      }
      if (!stream) {
        console.error('请输入码流');
        return;
      }
      if (!mediaServerIp) {
        console.error('请输入流媒体服务IP');
        return;
      }
      if (!mediaServerRestApiPort) {
        console.error('请输入流媒体服务rest端口');
        return;
      }
      let data = {
        mediaServerRestApiPort: mediaServerRestApiPort,
        mediaServerIp: mediaServerIp,
        cameraId: cameraId,
        cameraIp: cameraIp,
        userName: userName,
        password: password,
        channel: channel,
        stream: stream,
        startTime: startTime ? startTime : null,
        endTime: endTime ? endTime : null
      }
      console.log(data);
      let res = await fetch(`${contextPath}/media/play`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      });
      let resData = await res.json();
      if (resData.code === 200) {
        let devId = resData.data['devId']
        startPlayer(mediaServerIp, mediaServerWebPort, devId, cameraId);
      } else {
        console.error('添加失败' + resData.message);
      }
    }

    /**
     * 更新视频
     */
    function startPlayer(mediaServerIp, mediaServerWebPort, devId, cameraId) {
      initPlayer(mediaServerIp, mediaServerWebPort);
      startStream(devId);
      keepAlive(cameraId)
    }

    /**
     * 保持播放器活跃
     */
    async function keepAlive(cameraId) {
      let video = document.getElementById(`video${guidId}`);
      if (video && video.currentTime > 0 && !video.paused && !video.ended && video.readyState > 2) {
        console.log('keepAlive...');
        if (cameraId === false) {
          console.error('cameraId is empty');
          return;
        }
        let data = await fetch(`${contextPath}/media/keepAlive?cameraId=${cameraId}`, {method: 'POST'});
        let result = await data.json();
        if (result.code === 200) {
          console.log('keepAlive success');
        } else {
          console.error('keepAlive failed');
        }
      } else {
        console.warn('video not ready...');
      }
      setTimeout(() => {
        keepAlive(cameraId);
      }, 1000 * 10);
    }

    /**
     * 初始化播放器
     */
    function initPlayer(serverIp, webPort) {
      console.log('function initPlayer starting.');
      if (!serverIp) {
        console.error('serverIp is empty');
        return;
      }
      if (!webPort) {
        console.error('webPort is empty');
        return;
      }
      const url = `https://${serverIp}:${webPort}/`;
      if (ayPlayer) {
        stopStream();
      }
      ayPlayer = new AyPlayerMain(`video${guidId}`);
      //初始化视频源，并启动工作线程
      try {
        ayPlayer.initAndStartWorker(url);
      } catch (err) {
        console.error('ayPlayer.initAndStartWorker failed. err=%o', err);
      }
      console.log('function initPlayer finished.');
    }

    /**
     * 开始播放
     */
    function startStream(streamId) {
      if (ayPlayer.isReady()) {
        ayPlayer.startStream(streamId);
      } else {
        setTimeout(() => {
          startStream(streamId);
        }, 1000);
      }
    }

    /**
     * 停止播放
     */
    function stopStream() {
      if (ayPlayer) {
        ayPlayer.stopStream();
      }
    }

    onMounted(() => {
      playByCamera(props.cameraInfo)
    })

    onUnmounted(() => {
      stopStream();
    })

    return {
      guidId: guidId
    }
  }
})


</script>


<style scoped>
.test1 {
  height: 100%;
}
</style>