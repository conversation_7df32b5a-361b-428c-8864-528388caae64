<template>
  <div class="test1">
    <div class="test2" v-for="(item, index) in cameraInfos" :key="index">
      <PlayerFullComponent :cameraInfo="item"/>
    </div>
  </div>
</template>

<script setup>
import PlayerFullComponent from "@/components/PlayerFullComponent.vue";
import {ref} from "vue";
let cameraInfos = new ref([{
  cameraId: '************', // 相机ID
  cameraIp: '************', // 相机IP
  userName: 'admin', // 相机用户名
  password: 'Allin2018', // 相机密码
  channel: 1,  // 通道
  stream: 1,  // 码流
  startTime: '20240327T102600Z', // 开始时间 (直播可不传)
  endTime: '20240327T152400Z', // 结束时间 (直播可不传)
  mediaServerIp: 'bird.allin-tech.com', // 流媒体服务器IP
  mediaServerRestApiPort: 9191, // 流媒体服务器RestApi端口
  mediaServerWebPort: 9193 // 流媒体web端口
},{
  cameraId: '************', // 相机ID
  cameraIp: '************', // 相机IP
  userName: 'admin', // 相机用户名
  password: 'Allin2018', // 相机密码
  channel: 1,  // 通道
  stream: 1,  // 码流
  startTime: '20240327T102600Z', // 开始时间 (直播可不传)
  endTime: '20240327T152400Z', // 结束时间 (直播可不传)
  mediaServerIp: 'bird.allin-tech.com', // 流媒体服务器IP
  mediaServerRestApiPort: 9191, // 流媒体服务器RestApi端口
  mediaServerWebPort: 9193 // 流媒体web端口
},{
  cameraId: '************-2', // 相机ID
  cameraIp: '************-2', // 相机IP
  userName: 'admin', // 相机用户名
  password: 'Allin2018', // 相机密码
  channel: 1,  // 通道
  stream: 1,  // 码流
  startTime: '20240327T102600Z', // 开始时间 (直播可不传)
  endTime: '20240327T152400Z', // 结束时间 (直播可不传)
  mediaServerIp: 'bird.allin-tech.com', // 流媒体服务器IP
  mediaServerRestApiPort: 9191, // 流媒体服务器RestApi端口
  mediaServerWebPort: 9193 // 流媒体web端口
},{
  cameraId: '************-3', // 相机ID
  cameraIp: '************-3', // 相机IP
  userName: 'admin', // 相机用户名
  password: 'Allin2018', // 相机密码
  channel: 1,  // 通道
  stream: 1,  // 码流
  startTime: '20240327T102600Z', // 开始时间 (直播可不传)
  endTime: '20240327T152400Z', // 结束时间 (直播可不传)
  mediaServerIp: 'bird.allin-tech.com', // 流媒体服务器IP
  mediaServerRestApiPort: 9191, // 流媒体服务器RestApi端口
  mediaServerWebPort: 9193 // 流媒体web端口
},{
  cameraId: '************', // 相机ID
  cameraIp: '************', // 相机IP
  userName: 'admin', // 相机用户名
  password: 'Allin2018', // 相机密码
  channel: 1,  // 通道
  stream: 1,  // 码流
  startTime: '20240327T102600Z', // 开始时间 (直播可不传)
  endTime: '20240327T152400Z', // 结束时间 (直播可不传)
  mediaServerIp: 'bird.allin-tech.com', // 流媒体服务器IP
  mediaServerRestApiPort: 9191, // 流媒体服务器RestApi端口
  mediaServerWebPort: 9193 // 流媒体web端口
},{
  cameraId: '************', // 相机ID
  cameraIp: '************', // 相机IP
  userName: 'admin', // 相机用户名
  password: 'Allin2018', // 相机密码
  channel: 1,  // 通道
  stream: 1,  // 码流
  startTime: '20240327T102600Z', // 开始时间 (直播可不传)
  endTime: '20240327T152400Z', // 结束时间 (直播可不传)
  mediaServerIp: 'bird.allin-tech.com', // 流媒体服务器IP
  mediaServerRestApiPort: 9191, // 流媒体服务器RestApi端口
  mediaServerWebPort: 9193 // 流媒体web端口
},{
  cameraId: '************-2', // 相机ID
  cameraIp: '************-2', // 相机IP
  userName: 'admin', // 相机用户名
  password: 'Allin2018', // 相机密码
  channel: 1,  // 通道
  stream: 1,  // 码流
  startTime: '20240327T102600Z', // 开始时间 (直播可不传)
  endTime: '20240327T152400Z', // 结束时间 (直播可不传)
  mediaServerIp: 'bird.allin-tech.com', // 流媒体服务器IP
  mediaServerRestApiPort: 9191, // 流媒体服务器RestApi端口
  mediaServerWebPort: 9193 // 流媒体web端口
},{
  cameraId: '************-3', // 相机ID
  cameraIp: '************-3', // 相机IP
  userName: 'admin', // 相机用户名
  password: 'Allin2018', // 相机密码
  channel: 1,  // 通道
  stream: 1,  // 码流
  startTime: '20240327T102600Z', // 开始时间 (直播可不传)
  endTime: '20240327T152400Z', // 结束时间 (直播可不传)
  mediaServerIp: 'bird.allin-tech.com', // 流媒体服务器IP
  mediaServerRestApiPort: 9191, // 流媒体服务器RestApi端口
  mediaServerWebPort: 9193 // 流媒体web端口
},{
  cameraId: '************', // 相机ID
  cameraIp: '************', // 相机IP
  userName: 'admin', // 相机用户名
  password: 'Allin2018', // 相机密码
  channel: 1,  // 通道
  stream: 1,  // 码流
  startTime: '20240327T102600Z', // 开始时间 (直播可不传)
  endTime: '20240327T152400Z', // 结束时间 (直播可不传)
  mediaServerIp: 'bird.allin-tech.com', // 流媒体服务器IP
  mediaServerRestApiPort: 9191, // 流媒体服务器RestApi端口
  mediaServerWebPort: 9193 // 流媒体web端口
},{
  cameraId: '************', // 相机ID
  cameraIp: '************', // 相机IP
  userName: 'admin', // 相机用户名
  password: 'Allin2018', // 相机密码
  channel: 1,  // 通道
  stream: 1,  // 码流
  startTime: '20240327T102600Z', // 开始时间 (直播可不传)
  endTime: '20240327T152400Z', // 结束时间 (直播可不传)
  mediaServerIp: 'bird.allin-tech.com', // 流媒体服务器IP
  mediaServerRestApiPort: 9191, // 流媒体服务器RestApi端口
  mediaServerWebPort: 9193 // 流媒体web端口
},{
  cameraId: '************-2', // 相机ID
  cameraIp: '************-2', // 相机IP
  userName: 'admin', // 相机用户名
  password: 'Allin2018', // 相机密码
  channel: 1,  // 通道
  stream: 1,  // 码流
  startTime: '20240327T102600Z', // 开始时间 (直播可不传)
  endTime: '20240327T152400Z', // 结束时间 (直播可不传)
  mediaServerIp: 'bird.allin-tech.com', // 流媒体服务器IP
  mediaServerRestApiPort: 9191, // 流媒体服务器RestApi端口
  mediaServerWebPort: 9193 // 流媒体web端口
},{
  cameraId: '************-3', // 相机ID
  cameraIp: '************-3', // 相机IP
  userName: 'admin', // 相机用户名
  password: 'Allin2018', // 相机密码
  channel: 1,  // 通道
  stream: 1,  // 码流
  startTime: '20240327T102600Z', // 开始时间 (直播可不传)
  endTime: '20240327T152400Z', // 结束时间 (直播可不传)
  mediaServerIp: 'bird.allin-tech.com', // 流媒体服务器IP
  mediaServerRestApiPort: 9191, // 流媒体服务器RestApi端口
  mediaServerWebPort: 9193 // 流媒体web端口
},{
  cameraId: '************', // 相机ID
  cameraIp: '************', // 相机IP
  userName: 'admin', // 相机用户名
  password: 'Allin2018', // 相机密码
  channel: 1,  // 通道
  stream: 1,  // 码流
  startTime: '20240327T102600Z', // 开始时间 (直播可不传)
  endTime: '20240327T152400Z', // 结束时间 (直播可不传)
  mediaServerIp: 'bird.allin-tech.com', // 流媒体服务器IP
  mediaServerRestApiPort: 9191, // 流媒体服务器RestApi端口
  mediaServerWebPort: 9193 // 流媒体web端口
},{
  cameraId: '************', // 相机ID
  cameraIp: '************', // 相机IP
  userName: 'admin', // 相机用户名
  password: 'Allin2018', // 相机密码
  channel: 1,  // 通道
  stream: 1,  // 码流
  startTime: '20240327T102600Z', // 开始时间 (直播可不传)
  endTime: '20240327T152400Z', // 结束时间 (直播可不传)
  mediaServerIp: 'bird.allin-tech.com', // 流媒体服务器IP
  mediaServerRestApiPort: 9191, // 流媒体服务器RestApi端口
  mediaServerWebPort: 9193 // 流媒体web端口
},{
  cameraId: '************-2', // 相机ID
  cameraIp: '************-2', // 相机IP
  userName: 'admin', // 相机用户名
  password: 'Allin2018', // 相机密码
  channel: 1,  // 通道
  stream: 1,  // 码流
  startTime: '20240327T102600Z', // 开始时间 (直播可不传)
  endTime: '20240327T152400Z', // 结束时间 (直播可不传)
  mediaServerIp: 'bird.allin-tech.com', // 流媒体服务器IP
  mediaServerRestApiPort: 9191, // 流媒体服务器RestApi端口
  mediaServerWebPort: 9193 // 流媒体web端口
},{
  cameraId: '************-3', // 相机ID
  cameraIp: '************-3', // 相机IP
  userName: 'admin', // 相机用户名
  password: 'Allin2018', // 相机密码
  channel: 1,  // 通道
  stream: 1,  // 码流
  startTime: '20240327T102600Z', // 开始时间 (直播可不传)
  endTime: '20240327T152400Z', // 结束时间 (直播可不传)
  mediaServerIp: 'bird.allin-tech.com', // 流媒体服务器IP
  mediaServerRestApiPort: 9191, // 流媒体服务器RestApi端口
  mediaServerWebPort: 9193 // 流媒体web端口
},{
  cameraId: '************', // 相机ID
  cameraIp: '************', // 相机IP
  userName: 'admin', // 相机用户名
  password: 'Allin2018', // 相机密码
  channel: 1,  // 通道
  stream: 1,  // 码流
  startTime: '20240327T102600Z', // 开始时间 (直播可不传)
  endTime: '20240327T152400Z', // 结束时间 (直播可不传)
  mediaServerIp: 'bird.allin-tech.com', // 流媒体服务器IP
  mediaServerRestApiPort: 9191, // 流媒体服务器RestApi端口
  mediaServerWebPort: 9193 // 流媒体web端口
},{
  cameraId: '************', // 相机ID
  cameraIp: '************', // 相机IP
  userName: 'admin', // 相机用户名
  password: 'Allin2018', // 相机密码
  channel: 1,  // 通道
  stream: 1,  // 码流
  startTime: '20240327T102600Z', // 开始时间 (直播可不传)
  endTime: '20240327T152400Z', // 结束时间 (直播可不传)
  mediaServerIp: 'bird.allin-tech.com', // 流媒体服务器IP
  mediaServerRestApiPort: 9191, // 流媒体服务器RestApi端口
  mediaServerWebPort: 9193 // 流媒体web端口
},{
  cameraId: '************-2', // 相机ID
  cameraIp: '************-2', // 相机IP
  userName: 'admin', // 相机用户名
  password: 'Allin2018', // 相机密码
  channel: 1,  // 通道
  stream: 1,  // 码流
  startTime: '20240327T102600Z', // 开始时间 (直播可不传)
  endTime: '20240327T152400Z', // 结束时间 (直播可不传)
  mediaServerIp: 'bird.allin-tech.com', // 流媒体服务器IP
  mediaServerRestApiPort: 9191, // 流媒体服务器RestApi端口
  mediaServerWebPort: 9193 // 流媒体web端口
},{
  cameraId: '************-3', // 相机ID
  cameraIp: '************-3', // 相机IP
  userName: 'admin', // 相机用户名
  password: 'Allin2018', // 相机密码
  channel: 1,  // 通道
  stream: 1,  // 码流
  startTime: '20240327T102600Z', // 开始时间 (直播可不传)
  endTime: '20240327T152400Z', // 结束时间 (直播可不传)
  mediaServerIp: 'bird.allin-tech.com', // 流媒体服务器IP
  mediaServerRestApiPort: 9191, // 流媒体服务器RestApi端口
  mediaServerWebPort: 9193 // 流媒体web端口
},{
  cameraId: '************', // 相机ID
  cameraIp: '************', // 相机IP
  userName: 'admin', // 相机用户名
  password: 'Allin2018', // 相机密码
  channel: 1,  // 通道
  stream: 1,  // 码流
  startTime: '20240327T102600Z', // 开始时间 (直播可不传)
  endTime: '20240327T152400Z', // 结束时间 (直播可不传)
  mediaServerIp: 'bird.allin-tech.com', // 流媒体服务器IP
  mediaServerRestApiPort: 9191, // 流媒体服务器RestApi端口
  mediaServerWebPort: 9193 // 流媒体web端口
},{
  cameraId: '************', // 相机ID
  cameraIp: '************', // 相机IP
  userName: 'admin', // 相机用户名
  password: 'Allin2018', // 相机密码
  channel: 1,  // 通道
  stream: 1,  // 码流
  startTime: '20240327T102600Z', // 开始时间 (直播可不传)
  endTime: '20240327T152400Z', // 结束时间 (直播可不传)
  mediaServerIp: 'bird.allin-tech.com', // 流媒体服务器IP
  mediaServerRestApiPort: 9191, // 流媒体服务器RestApi端口
  mediaServerWebPort: 9193 // 流媒体web端口
},{
  cameraId: '************-2', // 相机ID
  cameraIp: '************-2', // 相机IP
  userName: 'admin', // 相机用户名
  password: 'Allin2018', // 相机密码
  channel: 1,  // 通道
  stream: 1,  // 码流
  startTime: '20240327T102600Z', // 开始时间 (直播可不传)
  endTime: '20240327T152400Z', // 结束时间 (直播可不传)
  mediaServerIp: 'bird.allin-tech.com', // 流媒体服务器IP
  mediaServerRestApiPort: 9191, // 流媒体服务器RestApi端口
  mediaServerWebPort: 9193 // 流媒体web端口
},{
  cameraId: '************-3', // 相机ID
  cameraIp: '************-3', // 相机IP
  userName: 'admin', // 相机用户名
  password: 'Allin2018', // 相机密码
  channel: 1,  // 通道
  stream: 1,  // 码流
  startTime: '20240327T102600Z', // 开始时间 (直播可不传)
  endTime: '20240327T152400Z', // 结束时间 (直播可不传)
  mediaServerIp: 'bird.allin-tech.com', // 流媒体服务器IP
  mediaServerRestApiPort: 9191, // 流媒体服务器RestApi端口
  mediaServerWebPort: 9193 // 流媒体web端口
}]);
</script>


<style scoped>
.test1 {
  height: 100%;
  overflow: auto;
  display: flex;
  flex-wrap: wrap;
  align-content: start;
  .test2 {
    width: 25%;
    height: 25%;
    border: 1px solid gold;
    box-sizing: border-box;
  }
}
</style>