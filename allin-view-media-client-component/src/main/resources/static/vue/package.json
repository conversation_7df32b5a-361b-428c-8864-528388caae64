{"name": "play-test", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"core-js": "^3.8.3", "less": "3.9.0", "less-loader": "4.1.0", "vue": "^3.2.13", "vue-router": "^4.0.3"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@babel/plugin-transform-private-methods": "^7.24.1", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "style-resources-loader": "^1.4.1", "vue-cli-plugin-style-resources-loader": "~0.1.5"}, "eslintConfig": {"root": false, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}