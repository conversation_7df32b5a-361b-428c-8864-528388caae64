package com.allin.view.media.client.controller;


import com.allin.view.base.domain.Result;
import com.allin.view.media.client.config.MediaProperties;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("media/sdk/version")
public class MediaSdkVersionController {

    private final MediaProperties config;

    public MediaSdkVersionController(MediaProperties config) {
        this.config = config;
    }


    @GetMapping
    public Result<Integer> getVersion() {
        return Result.ok(config.getMediaSdkVersion());
    }

}
