package com.allin.view.media.client.pojo.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class DeviceDto {

    /**
     * 设备id
     */
    private Integer devId;

    /**
     * 设备url
     */
    @NotBlank(message = "设备url不能为空")
    private String devUrl;

    /**
     * 设备编号
     */
    @NotBlank(message = "设备编号不能为空")
    private String devNum;

    /**
     * 状态
     */
    @NotNull(message = "状态不能为空")
    private Integer status;
}
