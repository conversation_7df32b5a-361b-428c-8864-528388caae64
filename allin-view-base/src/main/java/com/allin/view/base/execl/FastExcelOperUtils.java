package com.allin.view.base.execl;

import cn.hutool.core.io.FileUtil;
import cn.idev.excel.ExcelWriter;
import cn.idev.excel.FastExcel;
import cn.idev.excel.write.builder.ExcelWriterBuilder;
import cn.idev.excel.write.handler.WriteHandler;
import cn.idev.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson2.JSON;
import com.allin.view.base.constant.FileConstants;
import com.allin.view.base.domain.Result;
import com.allin.view.base.execl.handler.ImageCellWriteHandler;
import com.allin.view.base.utils.file.FileNameUtils;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * fastexcel 封装工具类
 *
 * <AUTHOR>
 * @see <a href="https://github.com/fast-excel/fastexcel">fastExcel 文档</a>
 */
@Slf4j
public class FastExcelOperUtils {

    /**
     * 每页最大导出行数
     */
    private static final int maxRows = 5000;

    /**
     * 支持图片的导出
     */
    public static <T> void exportImgXlsx(HttpServletResponse response,
                                         List<T> dataList,
                                         Class<T> clazz,
                                         String fileName) {
        exportXlsx(response, dataList, clazz, Collections.emptySet(), new ImageCellWriteHandler(), fileName);
    }

    /**
     * 将数据导出为 Excel 并写入 HttpServletResponse 返回给客户端
     *
     * @param response 响应对象
     * @param clazz    数据类的类型
     * @param fileName 导出文件的文件名
     * @param dataList 数据列表
     * @param <T>      数据类型
     */
    public static <T> void exportXlsx(HttpServletResponse response, List<T> dataList, Class<T> clazz, String fileName) {
        exportXlsx(response, dataList, clazz, Collections.emptySet(), null, fileName);
    }

    /**
     * 将数据导出为 Excel 并写入 HttpServletResponse 返回给客户端
     * 需要自己构建 ExcelWriter，参考如下，自定义头：
     * <pre>{@code
     *         List<List<String>> head = new ArrayList<>();
     *         List<String> head0 = new ArrayList<>();
     *         head0.add("类型");
     *         List<String> head1 = new ArrayList<>();
     *         head1.add("次数");
     *         head.add(head0);
     *         head.add(head1);
     *         final ExcelWriterBuilder write = FastExcel.write(response.getOutputStream(), TargetStatisticsVo.class).head(head);
     * }</pre>
     *
     * @param response 响应对象
     * @param dataList 数据列表
     * @param write    excel写构造器
     * @param fileName 文件名
     * @param <T>      数据类型
     */
    public static <T> void exportXlsx(HttpServletResponse response,
                                      List<T> dataList,
                                      ExcelWriterBuilder write,
                                      String fileName) {
        try {
            fileName = FileNameUtils.getUrlEncodeFileName(fileName, ".xlsx");
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            response.setCharacterEncoding("utf-8");
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + fileName);
            write.autoCloseStream(Boolean.FALSE)
                    .sheet("数据页")
                    .doWrite(dataList);
        } catch (Exception e) {
            // 处理异常
            log.error("导出文件失败", e);
            // 重置response
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            try {
                response.getWriter().println(JSON.toJSONString(Result.fail(e.getMessage())));
            } catch (IOException ex) {
                log.error("导出文件失败", e);
            }
        }
    }


    /**
     * 将数据导出为 Excel 并写入 HttpServletResponse 返回给客户端
     *
     * @param response                响应对象
     * @param dataList                数据列表
     * @param clazz                   数据类的类型
     * @param excludeColumnFiledNames 需要忽略的列
     * @param writeHandlers           拦截器
     * @param <T>                     数据类型
     * @param fileName                导出文件的文件名
     */
    public static <T> void exportXlsx(HttpServletResponse response,
                                      List<T> dataList,
                                      Class<T> clazz,
                                      Set<String> excludeColumnFiledNames,
                                      WriteHandler writeHandlers,
                                      String fileName) {
        try {
            fileName = FileNameUtils.getUrlEncodeFileName(fileName, ".xlsx");
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            response.setCharacterEncoding("utf-8");
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + fileName);
            if (dataList.size() <= maxRows) {
                final ExcelWriterBuilder write = FastExcel.write(response.getOutputStream(), clazz);
                if (Objects.nonNull(writeHandlers)) {
                    write.registerWriteHandler(writeHandlers);
                }
                write.excludeColumnFieldNames(excludeColumnFiledNames)
                        .autoCloseStream(Boolean.FALSE)
                        .sheet("数据页")
                        .doWrite(dataList);
                return;
            }

            // 分批写入多个sheet
            ExcelWriter excelWriter = FastExcel.write(response.getOutputStream(), clazz)
                    .autoCloseStream(Boolean.FALSE)
                    .build();
            // 总条数
            int totalRows = dataList.size();
            // 需要的页码数
            int sheets = (int) Math.ceil((double) totalRows / maxRows);

            for (int i = 0; i < sheets; i++) {
                // 计算每个sheet的数据范围
                int fromIndex = i * maxRows;
                int toIndex = Math.min(fromIndex + maxRows, totalRows);

                List<?> subList = dataList.subList(fromIndex, toIndex);
                WriteSheet writeSheet = FastExcel.writerSheet(i, "数据页" + (i + 1)).build();
                excelWriter.write(subList, writeSheet);
            }

            // 关闭writer
            excelWriter.finish();
        } catch (Exception e) {
            // 处理异常
            log.error("导出文件失败", e);
            // 重置response
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            try {
                response.getWriter().println(JSON.toJSONString(Result.fail(e.getMessage())));
            } catch (IOException ex) {
                log.error("导出文件失败", e);
            }
        }
    }

    /**
     * 创建一个excel文件，将数据写入excel文件对象, 并返回该文件对象
     */
    public static <T> File writeToLocalPath(List<T> dataList, Class<T> clazz, String fileName) {
        // 创建文件
        fileName = FileNameUtils.getUrlEncodeFileName(fileName, ".xlsx");
        final File tempFile = FileUtil.touch(FileConstants.TEMP_DIR, fileName);
        FastExcel.write(tempFile, clazz)
                .sheet("数据页")
                .doWrite(dataList);
        return tempFile;
    }

    /**
     * 填充excel模板
     */
    public static <T> File fillToTemplate(List<T> dataList, InputStream templateInputStream, String fileName) {
        // 创建文件
        fileName = FileNameUtils.getUrlEncodeFileName(fileName, ".xlsx");
        final File tempFile = FileUtil.touch(FileConstants.TEMP_DIR, fileName);
        FastExcel.write(tempFile)
                .withTemplate(templateInputStream)
                .sheet()
                .doFill(dataList);
        return tempFile;
    }

}
