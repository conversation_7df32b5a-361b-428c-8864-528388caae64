package com.allin.view.base.execl.converter;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * EasyExcel List和String互相转换注解
 *
 * <AUTHOR>
 * @since 2024/6/3
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ListToStrDelimiter {

    /**
     * 指定分隔符，默认为","
     */
    String delimiter() default ",";

}
