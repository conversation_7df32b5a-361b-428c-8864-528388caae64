package com.allin.view.base.i18n;

import com.alibaba.fastjson2.JSON;
import org.apache.commons.logging.Log;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.boot.logging.DeferredLogFactory;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.PropertiesPropertySource;

import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

/**
 * base 模块国际化生效配置
 *
 * <AUTHOR>
 * @since 2025/4/23
 */
public class BaseI18nEnvEnvironmentPostProcessor implements EnvironmentPostProcessor {

    private final Log log;

    public BaseI18nEnvEnvironmentPostProcessor(DeferredLogFactory logFactory) {
        log = logFactory.getLog(BaseI18nEnvEnvironmentPostProcessor.class);
    }

    @Override
    public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
        // 使用 Binder 获取 List 配置
        List<String> basenames = Binder.get(environment).bind("spring.messages.basename", List.class)
                .orElse(new ArrayList<String>());
        Properties customProperties = new Properties();
        if (!basenames.contains("i18n.allin_view_base")) {
            basenames.add("i18n.allin_view_base");
            log.info("当前国际化配置列表:" + JSON.toJSONString(basenames));
        }
        customProperties.put("spring.messages.basename", String.join(",", basenames));
        environment.getPropertySources().addFirst(new PropertiesPropertySource("i18nProperties", customProperties));
    }

}