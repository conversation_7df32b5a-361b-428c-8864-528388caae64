package com.allin.view.base.exception.service;

import com.allin.view.base.exception.BaseException;

/**
 * 通用校验异常类.
 * 在各种校验不通过时抛出.
 * <p>
 * 校验类异常的 code 都为 400
 *
 * <AUTHOR>
 * @date 2023/11/14
 */
public class ValidationFailureException extends BaseException {

    public ValidationFailureException(String msg) {
        this(400, msg, null);
    }

    public ValidationFailureException(Integer code, String msg) {
        this(code, msg, null);
    }

    public ValidationFailureException(String msg, Throwable e) {
        this(400, msg, e);
    }

    public ValidationFailureException(Integer code, String msg, Throwable e) {
        super(code, msg, e);
    }
}