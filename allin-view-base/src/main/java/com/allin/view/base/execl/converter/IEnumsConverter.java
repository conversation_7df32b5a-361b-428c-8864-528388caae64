package com.allin.view.base.execl.converter;


import com.allin.view.base.enums.base.IEnums;
import com.allin.view.base.enums.base.InvalidIEnums;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * EasyExcel 枚举转换注解
 *
 * <AUTHOR>
 * @date 2023/9/25
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface IEnumsConverter {

    /**
     * 值的替换, 返回结果集是{code1_desc1,code2_desc2}，接收前端参数反过来是{desc1_code1,desc2_code2},所以只用写一个
     */
    String[] replace() default {};

    /**
     * 用来转换的枚举类
     */
    Class<? extends IEnums> enums() default InvalidIEnums.class;

}
