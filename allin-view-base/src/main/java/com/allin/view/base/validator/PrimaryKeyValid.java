package com.allin.view.base.validator;

import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;

/**
 * 校验主键是否存在
 * author: 郭国勇
 */

@Target({FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = {PrimaryKeyValidator.class})
public @interface PrimaryKeyValid {

    String message() default "非法的主键";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    Class<? extends IService<?>> service();

    /**
     * 是否开启不能为空的校验，默认关闭
     */
    boolean isNotBlank() default false;
}
