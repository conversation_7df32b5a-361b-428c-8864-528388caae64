package com.allin.view.base.enums;


import com.allin.view.base.enums.base.IEnums;

/**
 * 通用状态枚举
 * @deprecated 含义不明确，建议各项目根据实际情况自定义封装
 **/
@Deprecated
public enum StatusEnums implements IEnums {
    /**
     * 0 禁用
     */
    DISABLE(0, "禁用"),
    /**
     * 1 启用
     */
    ENABLE(1, "启用");

    private final Integer code;

    private final String desc;

    StatusEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}

