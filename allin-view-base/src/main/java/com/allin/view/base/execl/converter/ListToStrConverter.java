package com.allin.view.base.execl.converter;

import cn.hutool.core.util.StrUtil;
import cn.idev.excel.converters.Converter;
import cn.idev.excel.converters.ReadConverterContext;
import cn.idev.excel.converters.WriteConverterContext;
import cn.idev.excel.enums.CellDataTypeEnum;
import cn.idev.excel.metadata.data.WriteCellData;

import java.lang.reflect.Field;
import java.util.Collections;
import java.util.List;

/**
 * EasyExcel List<String>转换字符串逗号拼接
 *
 * <AUTHOR>
 * @date 2023/9/25
 */
public class ListToStrConverter implements Converter<List<String>> {


    @Override
    public Class<?> supportJavaTypeKey() {
        return List.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<List<String>> context) {
        final Field field = context.getContentProperty().getField();
        final ListToStrDelimiter listToStrDelimiter = field.getAnnotation(ListToStrDelimiter.class);
        if (listToStrDelimiter != null) {
            return new WriteCellData<>(String.join(listToStrDelimiter.delimiter(), context.getValue()));
        }
        return new WriteCellData<>(String.join(",", context.getValue()));
    }

    @Override
    public List<String> convertToJavaData(ReadConverterContext<?> context) {
        String value = "";
        final CellDataTypeEnum typeEnum = context.getReadCellData().getType();
        if (typeEnum == CellDataTypeEnum.STRING) {
            value = context.getReadCellData().getStringValue();
        }
        if (typeEnum == CellDataTypeEnum.NUMBER) {
            value = context.getReadCellData().getNumberValue().toString();
        }

        final Field field = context.getContentProperty().getField();
        if (StrUtil.isBlank(value)) {
            return Collections.emptyList();
        }
        final ListToStrDelimiter listToStrDelimiter = field.getAnnotation(ListToStrDelimiter.class);
        if (listToStrDelimiter != null) {
            return StrUtil.splitTrim(value, listToStrDelimiter.delimiter());
        }
        return StrUtil.splitTrim(value, ",");
    }
}
