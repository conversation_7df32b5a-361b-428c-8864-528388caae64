package com.allin.view.base.execl.listener;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.exception.ExcelDataConvertException;
import cn.idev.excel.read.listener.ReadListener;
import com.alibaba.fastjson2.JSON;
import com.allin.view.base.exception.service.ProgramException;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 导入监听器抽象类
 *
 * <AUTHOR>
 * @date 2023/11/7
 */
@Slf4j
public abstract class AbstractImportListener<T, U extends IErrorData> implements ReadListener<T> {

    /**
     * 每1000条保存一次
     */
    protected static final int BATCH_COUNT = 1000;

    /**
     * 参数校验
     */
    protected final Validator validator = Validation.buildDefaultValidatorFactory().getValidator();

    /**
     * 成功总数
     */
    @Getter
    protected Long succTotal = 0L;

    /**
     * 非法数据
     */
    @Getter
    protected List<U> errorDataList = new ArrayList<>();

    protected abstract U createErrorDataInstance();

    protected abstract void saveData();

    protected void defaultSaveData(){
        try {
            saveData();
        } catch (Exception e){
            throw new ProgramException("保存数据异常", e);
        }
    }

    /**
     * 添加非法数据
     *
     * @param data 非法数据
     */
    protected void addErrorData(T data, String errorMsg) {
        U errorData = createErrorDataInstance();
        if (Objects.nonNull(data)) {
            BeanUtils.copyProperties(data, errorData);
        }
        errorData.setErrorMsg(errorMsg);
        errorDataList.add(errorData);
    }

    /**
     * 直接覆盖该方法实现，在处理数据前会默认输出日志
     *
     * <AUTHOR>
     * @date 2023/11/9
     */
    protected void invokeHandle(T data, AnalysisContext context) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void invoke(T data, AnalysisContext context) {
        log.info("解析到一条数据:{}", JSON.toJSONString(data));
        try {
            invokeHandle(data, context);
        } catch (Exception e) {
            addErrorData(data, e.getMessage());
        }
    }

    /**
     * 所有数据解析完成了 都会来调用
     * 这里也要保存数据，确保最后遗留的数据也存储到数据库
     *
     * @param context
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        defaultSaveData();
        log.info("所有数据解析完成！");
    }

    /**
     * 在转换异常 获取其他异常下会调用本接口。抛出异常则停止读取。如果这里不抛出异常则 继续读取下一行。
     */
    @Override
    public void onException(Exception exception, AnalysisContext context) {
        if (exception instanceof ProgramException) {
            throw (ProgramException) exception;
        }
        
        log.error("解析失败，但是继续解析下一行:{}", exception.getMessage());
        // 如果是某一个单元格的转换异常 能获取到具体行号
        // 如果要获取头的信息 配合invokeHeadMap使用
        if (exception instanceof ExcelDataConvertException) {
            ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException) exception;
            final String format = StrUtil.format("上传文档中的第{}行, 第{}列解析异常, 错误为: {}, 数据为: {}",
                    excelDataConvertException.getRowIndex(),
                    excelDataConvertException.getColumnIndex() + 1,
                    ExceptionUtil.getRootCauseMessage(excelDataConvertException),
                    excelDataConvertException.getCellData().getStringValue());
            addErrorData(null, format);
            log.error(format);
        }
    }
}
