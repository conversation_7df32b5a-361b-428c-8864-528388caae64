package com.allin.view.base.utils;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 新的时间工具类, 封装的时间操作不包括Date类的
 *
 * <AUTHOR>
 * @since 2024/7/30
 */
public class LocalDateUtils {

    public static final String TYPE_DAY = "day";

    public static final String TYPE_MONTH = "month";

    public static final String TYPE_YEAR = "year";

    public static String YYYY_MM_DD = "yyyy-MM-dd";

    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    /**
     * 通用日期解析方法
     */
    private static LocalDate parseDate(String date, String format) {
        if (StrUtil.isBlank(date)) {
            throw new IllegalArgumentException("日期不能为空");
        }

        if (StrUtil.isNotBlank(format)) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
            // 判断是否为 yyyy-MM 格式
            if (format.equals("yyyy-MM")) {
                return YearMonth.parse(date, formatter).atDay(1);
            } else {
                return LocalDate.parse(date, formatter);
            }
        } else {
            return DateUtil.parse(date).toLocalDateTime().toLocalDate();
        }
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static String datePath() {
        return DateFormatUtils.format(new Date(), "yyyy/MM/dd");
    }

    /**
     * 获取开始时间到结束时间之间的日期, 可以是年,月,日
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param type      获取的类型day,month,year
     * @return {@link ArrayList} [startDate,...,endDate]
     */
    public static List<String> getAllDateByParamDate(String startDate,
                                                     String endDate,
                                                     String format,
                                                     String type) {
        if (!TYPE_YEAR.equals(type) && !TYPE_MONTH.equals(type) && !TYPE_DAY.equals(type)) {
            return Collections.emptyList();
        }

        // 使用默认格式或指定格式解析日期
        LocalDate parseStartDate;
        LocalDate parseEndDate;
        try {
            parseStartDate = parseDate(startDate, format);
            parseEndDate = parseDate(endDate, format);
        } catch (Exception e) {
            throw new IllegalArgumentException("日期解析失败，请检查输入格式是否正确", e);
        }

        // 存储所有日期的list
        ArrayList<String> list = new ArrayList<>();

        // 获取所有年份
        if (type.equals(TYPE_YEAR)) {
            int startYear = parseStartDate.getYear();
            int endYear = parseEndDate.getYear();

            for (int year = startYear; year <= endYear; year++) {
                list.add(String.valueOf(year));
            }
        } else if (type.equals(TYPE_MONTH)) {
            // 获取所有月份
            LocalDate current = parseStartDate;
            while (!current.isAfter(parseEndDate)) {
                list.add(current.format(DatePattern.NORM_MONTH_FORMATTER));
                current = current.plusMonths(1);
            }
        } else {
            // 获取所有日期
            LocalDate current = parseStartDate;
            while (!current.isAfter(parseEndDate)) {
                list.add(current.format(DatePattern.NORM_DATE_FORMATTER));
                current = current.plusDays(1);
            }
        }

        // 返回数据
        return list;
    }

    /**
     * 获取开始时间到结束时间之间的日期
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param type      获取的类型day,month,year
     * @return {@link ArrayList} [startDate,...,endDate]
     */
    public static List<String> getAllDateByParamDate(String startDate, String endDate, String type) {
        return getAllDateByParamDate(startDate, endDate, null, type);
    }

    /**
     * 获取开始时间到结束时间之间的日期
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param type      获取的类型day,month,year
     * @return {@link ArrayList} [startDate,...,endDate]
     */
    public static List<String> getAllDateByParamDate(LocalDateTime startDate, LocalDateTime endDate, String type) {
        return getAllDateByParamDate(
                startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                null, type);
    }

    /**
     * 获取开始时间到结束时间之间的日期
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param type      获取的类型day,month,year
     * @return {@link ArrayList} [startDate,...,endDate]
     */
    public static List<String> getAllDateByParamDate(LocalDate startDate, LocalDate endDate, String type) {
        return getAllDateByParamDate(startDate.toString(), endDate.toString(), "yyyy-MM-dd", type);
    }

    /**
     * 获取开始时间到结束时间之间的月份
     */
    public static List<String> getAllMonthByYearMonth(String startDate, String endDate) {
        return getAllDateByParamDate(startDate, endDate, "yyyy-MM", TYPE_MONTH);
    }

    /**
     * 解析年月, 支持:yyyy-MM,yyyy/MM
     */
    public static DateTime parseYearMonth(String date) {
        return DateUtil.parse(date, "yyyy-MM", "yyyy/MM");
    }

    /**
     * 判断当前日期是否在闭区间内
     *
     * @param startDate 开始时间
     * @param endDate   截止时间
     */
    public static boolean currentDateIsIn(LocalDate startDate, LocalDate endDate) {
        final LocalDate now = LocalDate.now();
        return (now.equals(startDate) || now.isAfter(startDate))
                && (now.equals(endDate) || now.isBefore(endDate));
    }

    /**
     * 判断日期是否在闭区间内
     *
     * @param startDate 开始时间
     * @param endDate   截止时间
     */
    public static boolean dateIsIn(LocalDate date, LocalDate startDate, LocalDate endDate) {
        return (date.equals(startDate) || date.isAfter(startDate))
                && (date.equals(endDate) || date.isBefore(endDate));
    }

    /**
     * 判断当前时间是否在闭区间内
     *
     * @param startTime 开始时间
     * @param endTime   截止时间
     */
    public static boolean currentTimeIsIn(LocalTime startTime, LocalTime endTime) {
        final LocalTime now = LocalTime.now();
        return (now.equals(startTime) || now.isAfter(startTime))
                && (now.equals(endTime) || now.isBefore(endTime));
    }

    /**
     * 获取本季度第一天
     */
    public static LocalDate getFirstDayOfQuarter(LocalDate date) {
        int month = date.getMonthValue();
        int quarterStartMonth;
        if (month >= 1 && month <= 3) {
            quarterStartMonth = 1;
        } else if (month >= 4 && month <= 6) {
            quarterStartMonth = 4;
        } else if (month >= 7 && month <= 9) {
            quarterStartMonth = 7;
        } else {
            quarterStartMonth = 10;
        }
        return date.withMonth(quarterStartMonth).withDayOfMonth(1);
    }

    /**
     * 获取上季度第一天
     */
    public static LocalDate getFirstDayOfLastQuarter(LocalDate date) {
        int month = date.getMonthValue();
        int quarterStartMonth;
        if (month >= 1 && month <= 3) {
            date = date.minusYears(1);
            quarterStartMonth = 10;
        } else if (month >= 4 && month <= 6) {
            quarterStartMonth = 1;
        } else if (month >= 7 && month <= 9) {
            quarterStartMonth = 4;
        } else {
            quarterStartMonth = 7;
        }
        return date.withMonth(quarterStartMonth).withDayOfMonth(1);
    }
}
