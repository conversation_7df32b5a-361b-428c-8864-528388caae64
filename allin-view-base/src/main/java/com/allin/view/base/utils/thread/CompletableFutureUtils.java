package com.allin.view.base.utils.thread;

import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 用来封装一些 CompletableFuture 相关的操作实现
 *
 * <AUTHOR>
 * @date 2024/7/16
 */
public class CompletableFutureUtils {

    /**
     * 提交一批任务，并且阻塞等待该批任务全部执行完成才返回
     */
    public static void executeTasksAndJoin(List<Runnable> tasks, ThreadPoolTaskExecutor executor) {
        List<CompletableFuture<Void>> futures = new ArrayList<>(tasks.size());
        for (Runnable task : tasks) {
            futures.add(CompletableFuture.runAsync(task, executor));
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }
}
