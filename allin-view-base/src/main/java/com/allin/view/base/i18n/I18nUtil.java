package com.allin.view.base.i18n;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

import java.util.Locale;
import java.util.Map;

/**
 * 国际化工具类
 *
 * <AUTHOR>
 * @since 2025/4/12
 */
@Component
public class I18nUtil implements ApplicationContextAware {


    /**
     * 默认中文
     */
    public static final Locale defaultLocale = new Locale("zh");

    /**
     * 国际化标识转换
     */
    public static final Map<String, Locale> LocaleConvertMap = Map.of("zh-cn", defaultLocale);

    private static MessageSource messageSource;

    public static String getMessage(String key) {
        return messageSource.getMessage(key, null, key, LocaleContextHolder.getLocale());
    }

    public static String getMessage(String key, Locale locale) {
        return messageSource.getMessage(key, null, key, locale == null ? defaultLocale : locale);
    }

    public static String getMessage(String key, String defaultMessage) {
        return messageSource.getMessage(key, null, defaultMessage == null ? key : defaultMessage, LocaleContextHolder.getLocale());
    }

    public static String getMessage(String key, String defaultMessage, Locale locale) {
        return messageSource.getMessage(key, null, defaultMessage == null ? key : defaultMessage, locale == null ? defaultLocale : locale);
    }

    public static String getMessage(String key, Object... args) {
        return messageSource.getMessage(key, args, key, LocaleContextHolder.getLocale());
    }

    public static String getMessage(String key, @Nullable Object[] placeHolders, String defaultMessage) {
        return messageSource.getMessage(key, placeHolders, defaultMessage == null ? key : defaultMessage, LocaleContextHolder.getLocale());
    }

    public static String getMessage(String key, @Nullable Object[] placeHolders, Locale locale) {
        return messageSource.getMessage(key, placeHolders, key, locale == null ? defaultLocale : locale);
    }

    public static String getMessage(String key, @Nullable Object[] placeHolders, String defaultMessage, Locale locale) {
        return messageSource.getMessage(key, placeHolders, defaultMessage == null ? key : defaultMessage, locale == null ? defaultLocale : locale);
    }

    /**
     * 参数不能为空
     */
    public static String isEmpty() {
        return isEmpty("");
    }

    /**
     * 参数不能为空
     *
     * @param filed 参数名
     */
    public static String isEmpty(String filed) {
        return getMessage("base.filed.param.empty", filed);
    }

    /**
     * 数据已存在
     */
    public static String isExist() {
        return isExist("");
    }

    /**
     * 数据已存在
     *
     * @param filed 参数名
     */
    public static String isExist(String filed) {
        return getMessage("base.filed.data.exist", filed);
    }

    /**
     * 数据不存在
     */
    public static String isNotExist() {
        return isNotExist("");
    }

    /**
     * 数据不存在
     *
     * @param filed 参数名
     */
    public static String isNotExist(String filed) {
        return getMessage("base.filed.data.not.exist", filed);
    }

    /**
     * 参数异常
     */
    public static String isParamException() {
        return isParamException("");
    }

    /**
     * 参数异常
     *
     * @param filed 参数名
     */
    public static String isParamException(String filed) {
        return getMessage("base.filed.param.exception", filed);
    }

    @Override
    public void setApplicationContext(@NonNull ApplicationContext applicationContext) throws BeansException {
        if (null == messageSource) {
            messageSource = applicationContext.getBean(MessageSource.class);
        }
    }
}
