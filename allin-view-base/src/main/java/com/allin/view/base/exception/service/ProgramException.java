package com.allin.view.base.exception.service;

import com.allin.view.base.exception.BaseException;

/**
 * 程序异常类.
 * 表示程序中出现的问题.
 *
 * <AUTHOR>
 * @date 2023/11/14
 */
public class ProgramException extends BaseException {

    public ProgramException(String msg) {
        this(500, msg, null);
    }

    public ProgramException(String msg, Throwable e) {
        this(500, msg, e);
    }

    public ProgramException(Integer code, String msg) {
        this(code, msg, null);
    }

    public ProgramException(Integer code, String msg, Throwable e) {
        super(code, msg, e);
    }
}