package com.allin.view.base.validator.annotation;

import com.allin.view.base.enums.base.IEnums;
import com.allin.view.base.validator.EnumValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;

/**
 * 校验入参是否为指定enum的值的注解
 * <p>
 * 请使用 IEnumValid，该注解命名有歧义
 *
 * <AUTHOR>
 * @see com.allin.view.base.enums.validator.IEnumValid
 */
@Deprecated
@Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = {EnumValidator.class})
public @interface EnumValid {

    String message() default "{base.param.exception}";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    Class<? extends IEnums> target();

    /**
     * 是否开启不能为空的校验，默认关闭
     */
    boolean isNotNull() default false;
}
