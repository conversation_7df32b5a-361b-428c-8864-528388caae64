package com.allin.view.dts.sdk.test;

import com.allin.view.dts.sdk.api.DtsMsgApi;
import com.allin.view.dts.sdk.client.DtsMsgClientFactory;

/**
 * <AUTHOR>
 */
public class TestGet {

    public static void main(String[] args) throws Exception {
        DtsMsgApi client = DtsMsgClientFactory.createClient();
        client.login();

        while (true) {
            String msg = client.get();
            if (msg.isEmpty()) {
                continue;
            }
            System.out.println("msg = " + msg);
            Thread.sleep(5000);
        }
    }

}
