package com.allin.view.dts.sdk.controller;

import com.allin.view.dts.sdk.DtsApplication;
import com.allin.view.dts.sdk.model.ConfigModel;
import com.allin.view.dts.sdk.util.DtsConfUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
public class TestController {

    @GetMapping("/getProp")
    public String test() {
        ConfigModel configModel = DtsConfUtil.getDtsConfig();
        return configModel.toString();
    }

    @GetMapping("/send")
    public String send(String message) {
        DtsApplication.DTS_MSG_API.send(message);
        return "success";
    }
}
