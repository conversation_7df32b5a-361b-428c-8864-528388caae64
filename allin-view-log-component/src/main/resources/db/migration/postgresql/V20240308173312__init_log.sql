CREATE TABLE "sys_operate_log" (
                                   "id" varchar(19) NOT NULL,
                                   "title" varchar(64) NOT NULL,
                                   "oper_desc" varchar(50) NOT NULL,
                                   "method" varchar(255) NOT NULL,
                                   "request_method" varchar(32) NOT NULL,
                                   "user_id" varchar(32) NOT NULL,
                                   "oper_url" varchar(512) NOT NULL,
                                   "oper_ip" varchar(32) NOT NULL,
                                   "param" varchar(3072),
                                   "result" varchar(3072),
                                   "status" int4 NOT NULL,
                                   "error_message" varchar(3072),
                                   "oper_time" timestamp NOT NULL,
                                   CONSTRAINT "_copy_8" PRIMARY KEY ("id")
);
CREATE INDEX "idx_oper_time_user_name" ON "sys_operate_log" USING btree (
    "oper_time" ASC,
    "user_id" ASC
    );
COMMENT ON COLUMN "sys_operate_log"."id" IS 'ID';
COMMENT ON COLUMN "sys_operate_log"."title" IS '模块标题';
COMMENT ON COLUMN "sys_operate_log"."oper_desc" IS '操作描述';
COMMENT ON COLUMN "sys_operate_log"."method" IS '方法名称';
COMMENT ON COLUMN "sys_operate_log"."request_method" IS '请求方式';
COMMENT ON COLUMN "sys_operate_log"."user_id" IS '用户唯一标识';
COMMENT ON COLUMN "sys_operate_log"."oper_url" IS '请求URL';
COMMENT ON COLUMN "sys_operate_log"."oper_ip" IS 'IP地址';
COMMENT ON COLUMN "sys_operate_log"."param" IS '请求参数';
COMMENT ON COLUMN "sys_operate_log"."result" IS '返回参数';
COMMENT ON COLUMN "sys_operate_log"."status" IS '操作状态 （0正常 1异常）';
COMMENT ON COLUMN "sys_operate_log"."error_message" IS '错误消息';
COMMENT ON COLUMN "sys_operate_log"."oper_time" IS '操作时间';
COMMENT ON TABLE "sys_operate_log" IS '系统用户操作日志 ';