package com.allin.view.log.pojo.query;

import com.allin.view.base.validator.constant.RegExConstant;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import org.hibernate.validator.constraints.Range;


/**
 * <AUTHOR>
 * @date 2023/9/1
 */
@Data
public class OperateLogQuery {

    /**
     * 操作时间-开始
     */
    @Pattern(regexp = RegExConstant.LOCAL_DATE_TIME, message = RegExConstant.LOCAL_DATE_TIME_MSG)
    private String startTime;

    /**
     * 操作时间-结束
     */
    @Pattern(regexp = RegExConstant.LOCAL_DATE_TIME, message = RegExConstant.LOCAL_DATE_TIME_MSG)
    private String endTime;

    /**
     * 操作人
     */
    private String userId;

    /**
     * 模块标题, 模糊查询
     */
    private String title;

    /**
     * 操作描述, 模糊查询
     */
    private String operDesc;

    /**
     * 0成功, 1失败
     */
    @Range(min = 0, max = 1)
    private Integer status;
}
