package com.allin.view.log.controller;

import cn.hutool.core.util.StrUtil;
import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.Result;
import com.allin.view.log.annotation.Log;
import com.allin.view.log.pojo.entity.SysOperateLog;
import com.allin.view.log.pojo.query.OperateLogQuery;
import com.allin.view.log.pojo.vo.SysOperateLogVo;
import com.allin.view.log.service.SysOperateLogService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.*;

import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;

/**
 * 日志组件/操作日志管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/operate_log")
public class SysOperateLogController {

    private final SysOperateLogService operateLogService;

    public SysOperateLogController(SysOperateLogService operateLogService) {
        this.operateLogService = operateLogService;
    }

    /**
     * 分页查询操作日志记录
     */
    @GetMapping("/page")
    public Result<PageData<SysOperateLogVo>> page(Page<SysOperateLog> page, OperateLogQuery query) {
        operateLogService.page(page, Wrappers.lambdaQuery(SysOperateLog.class)
                .between(StrUtil.isNotBlank(query.getStartTime()) && StrUtil.isNotBlank(query.getEndTime()),
                        SysOperateLog::getOperTime,
                        StrUtil.isNotBlank(query.getStartTime()) ? Timestamp.valueOf(query.getStartTime()) : null,
                        StrUtil.isNotBlank(query.getEndTime()) ? Timestamp.valueOf(query.getEndTime()) : null)
                .eq(StrUtil.isNotBlank(query.getUserId()), SysOperateLog::getUserId, query.getUserId())
                .like(StrUtil.isNotBlank(query.getTitle()), SysOperateLog::getTitle, query.getTitle())
                .like(StrUtil.isNotBlank(query.getOperDesc()), SysOperateLog::getOperDesc, query.getOperDesc())
                .eq(Objects.nonNull(query.getStatus()), SysOperateLog::getStatus, query.getStatus())
                .orderByDesc(SysOperateLog::getOperTime));
        final List<SysOperateLogVo> vos = page.getRecords().stream().map(SysOperateLogVo::from).toList();
        return Result.ok(PageData.getInstance(page, vos));
    }

    /**
     * 删除操作日志记录
     *
     * @param id 日志id
     */
    @Log(title = "操作日志", operDesc = "删除操作日志")
    @DeleteMapping("/{id}")
    public Result<String> delete(@PathVariable Long id) {
        return operateLogService.removeById(id) ? Result.ok() : Result.fail();
    }
}
