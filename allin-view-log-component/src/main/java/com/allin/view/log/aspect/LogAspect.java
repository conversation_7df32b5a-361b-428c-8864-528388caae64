package com.allin.view.log.aspect;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.filter.Filter;
import com.alibaba.fastjson2.filter.PropertyFilter;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.auth.pojo.dto.JwtToken;
import com.allin.view.base.domain.Result;
import com.allin.view.base.utils.ServletUtils;
import com.allin.view.base.utils.ip.IpV4Utils;
import com.allin.view.base.utils.thread.ThreadPoolUtils;
import com.allin.view.log.annotation.Log;
import com.allin.view.log.enums.BusinessStatus;
import com.allin.view.log.pojo.entity.SysOperateLog;
import com.allin.view.log.service.SysOperateLogService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 操作日志记录处理
 *
 * <AUTHOR>
 * @date 2023/8/31
 */
@Slf4j
@Aspect
@Component
public class LogAspect {

    /**
     * 最大消息长度
     */
    private final int MAX_MSG_LENGTH = 3000;

    /**
     * 日志线程池
     */
    private final ThreadPoolTaskExecutor THREAD_POOL_TASK_EXECUTOR
            = ThreadPoolUtils.createThreadPoolTaskExecutor("LogThread-", 5);

    private final SysOperateLogService operateLogService;

    /**
     * 序列化过滤
     */
    private final Filter filterObject = (PropertyFilter) (object, name, value) -> {
        if (value instanceof MultipartFile || value instanceof HttpServletRequest || value instanceof HttpServletResponse) {
            return false; // 返回false表示忽略此值
        }
        return true;
    };


    public LogAspect(SysOperateLogService operateLogService) {
        this.operateLogService = operateLogService;
    }

    /**
     * 处理完请求后执行
     *
     * @param joinPoint 切点
     */
    @AfterReturning(pointcut = "@annotation(logAnnotation)", returning = "jsonResult")
    public void doAfterReturning(JoinPoint joinPoint, Log logAnnotation, Object jsonResult) {
        handleLog(joinPoint, logAnnotation, null, jsonResult);
    }

    /**
     * 拦截异常操作
     *
     * @param joinPoint 切点
     * @param e         异常
     */
    @AfterThrowing(value = "@annotation(logAnnotation)", throwing = "e")
    public void doAfterThrowing(JoinPoint joinPoint, Log logAnnotation, Exception e) {
        handleLog(joinPoint, logAnnotation, e, null);
    }

    /**
     * 处理日志
     */
    protected void handleLog(final JoinPoint joinPoint, Log logAnnotation, final Exception e, Object jsonResult) {
        try {
            HttpServletRequest request = ServletUtils.getRequest();
            SysOperateLog operateLog = prepareOperateLog(joinPoint, logAnnotation, e, request, jsonResult);
            THREAD_POOL_TASK_EXECUTOR.execute(() -> {
                operateLogService.save(operateLog);
            });
        } catch (Exception exception) {
            log.error("操作日志记录异常", exception);
        }
    }

    /**
     * 生成操作日志对象
     */
    private SysOperateLog prepareOperateLog(JoinPoint joinPoint,
                                            Log logAnnotation,
                                            Exception e,
                                            HttpServletRequest request,
                                            Object jsonResult) {
        SysOperateLog operateLog = new SysOperateLog();
        String className = joinPoint.getTarget().getClass().getName();
        String methodName = joinPoint.getSignature().getName();

        operateLog.setOperTime(LocalDateTime.now());
        if (e != null) {
            operateLog.setStatus(BusinessStatus.FAIL.getCode());
        } else if (jsonResult instanceof Result res) {
            operateLog.setStatus(res.isOk()
                    ? BusinessStatus.SUCCESS.getCode()
                    : BusinessStatus.FAIL.getCode());
        } else {
            operateLog.setStatus(BusinessStatus.SUCCESS.getCode());
        }
        operateLog.setOperIp(IpV4Utils.getIpAddr(request));
        operateLog.setOperUrl(request.getRequestURI());
        operateLog.setMethod(className + "." + methodName + "()");
        operateLog.setRequestMethod(request.getMethod());
        operateLog.setTitle(logAnnotation.title());
        operateLog.setOperDesc(logAnnotation.operDesc());
        operateLog.setUserId(SecurityContextHolder.tryGetLoginUser().map(JwtToken::getUser_id).orElse("-"));
        operateLog.setErrorMessage(e != null ? StrUtil.subSufByLength(e.getMessage(), MAX_MSG_LENGTH) : null);

        setControllerMethodDescription(joinPoint, logAnnotation, operateLog, jsonResult);

        return operateLog;
    }

    /**
     * 获取注解中对方法的描述信息
     */
    public void setControllerMethodDescription(JoinPoint joinPoint,
                                               Log logAnnotation,
                                               SysOperateLog operateLog,
                                               Object jsonResult) {
        // 是否需要保存request，参数和值
        if (logAnnotation.isSaveRequestData()) {
            // 获取参数的信息，传入到数据库中。
            setRequestData(joinPoint, operateLog);
        }
        // 是否需要保存response，参数和值
        if (logAnnotation.isSaveResponseData() && Objects.nonNull(jsonResult)) {
            operateLog.setResult(StringUtils.substring(JSON.toJSONString(jsonResult), 0, MAX_MSG_LENGTH));
        }
    }

    /**
     * 获取请求的参数，放到log中
     *
     * @param operateLog 操作日志
     */
    private void setRequestData(JoinPoint joinPoint, SysOperateLog operateLog) {
        String params = convertArgsToJsonString(joinPoint.getArgs());
        // 不论何种情况，我们都使用StrUtil.subSufByLength截取并设置参数
        operateLog.setParam(StrUtil.subSufByLength(params, MAX_MSG_LENGTH));
    }

    /**
     * 参数拼装
     * 将参数类型作为key，参数内容作为value
     */
    private String convertArgsToJsonString(Object[] args) {
        if (Objects.isNull(args)) {
            return "";
        }
        final JSONObject jsonObject = new JSONObject();
        for (Object arg : args) {
            if (ObjectUtil.isNotNull(arg)) {
                jsonObject.put(arg.getClass().getSimpleName(), arg);
            }
        }
        return JSON.toJSONString(jsonObject, filterObject);
    }
}
