package com.allin.view.log.service;

import com.allin.view.log.pojo.entity.SysOperateLog;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;

/**
* <AUTHOR>
* @description 针对表【sys_operate_log(系统用户操作日志 )】的数据库操作Service
* @date 2023-08-31 09:59:29
*/
public interface SysOperateLogService extends IService<SysOperateLog> {

    /**
     * 删除指定时间之前的数据
     *
     * @param operTime 指定时间
     * @return 被删除的数量
     */
    Integer deleteAllByOperTimeBefore(LocalDateTime operTime);

}
