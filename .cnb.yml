"**":
  push:
    - env:
        ROBOT: >-
          https://open.feishu.cn/open-apis/bot/v2/hook/97e31c50-6464-4ff9-becc-5e1f43e91de5
      stages:
        - name: Branch 仓库通知
          script: |     
            PAYLOAD=$(cat <<EOF
            {
              "msg_type": "interactive",
              "card": {
                "header": {
                  "title": {
                    "tag": "plain_text",
                    "content": "Commit 通知"
                  },
                  "template": "blue"
                },
                "elements": [
                  {
                    "tag": "div",
                    "text": {
                      "tag": "lark_md",
                      "content": "**分支**: $CNB_BRANCH\n**标题**: $CNB_COMMIT_MESSAGE_TITLE\n**提交者**: $CNB_COMMITTER\n"
                    }
                  },
                  {
                    "tag": "action",
                    "actions": [
                      {
                        "tag": "button",
                        "text": {
                          "tag": "plain_text",
                          "content": "查看 Commit"
                        },
                        "type": "primary",
                        "url": "$CNB_EVENT_URL"
                      }
                    ]
                  }
                ]
              }
            }
            EOF
            )

            # 发送 POST 请求到飞书 Webhook
            curl -X POST -H "Content-Type: application/json" \
              -d "$PAYLOAD" "$ROBOT"