<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.5.3</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>

    <groupId>com.allin</groupId>
    <artifactId>allin-view</artifactId>
    <version>${revision}</version>
    <modules>
        <module>allin-view-auth</module>
        <module>allin-view-config</module>
        <module>allin-view-base</module>
        <module>allin-view-demo</module>
        <module>allin-view-tcp</module>
        <module>allin-view-udp</module>
        <module>allin-view-ws</module>
        <module>allin-view-dts</module>
        <module>allin-view-file-component</module>
        <module>allin-view-quartz-component</module>
        <module>allin-view-log-component</module>
        <module>allin-view-dict-component</module>
        <module>allin-view-workflow-component</module>
        <module>allin-view-code-generator</module>
        <module>allin-view-media-client-component</module>
        <module>allin-view-mybatis-security</module>
    </modules>

    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <revision>4.3.0-SNAPSHOT</revision>
        <!-- 版本依赖管理 -->
        <druid.version>1.2.23</druid.version>
        <fastjson2.version>2.0.56</fastjson2.version>
        <mybatis-plus.version>3.5.11</mybatis-plus.version>
        <mapstruct.version>1.6.3</mapstruct.version>
        <hutool.version>5.8.37</hutool.version>
        <jjwt.version>0.12.6</jjwt.version>
        <fastexcel.version>1.2.0</fastexcel.version>
        <transmittable-thread-local.version>2.14.5</transmittable-thread-local.version>
        <flowable.version>7.0.1</flowable.version>
        <flowable-json-converter.version>6.8.1</flowable-json-converter.version>
        <mysql-connector-java.version>8.0.33</mysql-connector-java.version>
        <redisson.version>3.45.1</redisson.version>
        <flyway.version>10.11.0</flyway.version>
        <flyway.postgresql.version>10.9.1</flyway.postgresql.version>
        <commons-io.version>2.16.1</commons-io.version>
        <tika-core.verison>3.2.1</tika-core.verison>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-bom</artifactId>
                <version>${mybatis-plus.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2-extension-spring6</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>

            <!-- 基础模块 start-->
            <dependency>
                <groupId>com.allin</groupId>
                <artifactId>allin-view-config</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.allin</groupId>
                <artifactId>allin-view-base</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.allin</groupId>
                <artifactId>allin-view-tcp</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.allin</groupId>
                <artifactId>allin-view-udp</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.allin</groupId>
                <artifactId>allin-view-ws</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.allin</groupId>
                <artifactId>allin-view-quartz</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.allin</groupId>
                <artifactId>allin-view-auth</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.allin</groupId>
                <artifactId>allin-view-auth-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.allin</groupId>
                <artifactId>allin-view-auth-service</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.allin</groupId>
                <artifactId>allin-view-dict-component</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.allin</groupId>
                <artifactId>allin-view-log-component</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.allin</groupId>
                <artifactId>allin-view-quartz-component</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.allin</groupId>
                <artifactId>allin-view-file-component</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.allin</groupId>
                <artifactId>allin-view-mybatis-security</artifactId>
                <version>${revision}</version>
            </dependency>
            <!--基础模块 end-->

            <!--工具类 start-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <!--工具类 end-->

            <!-- jwt start-->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-api</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-impl</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-jackson</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <!-- jwt end-->

            <!--数据库 start-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-3-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-core</artifactId>
                <version>${flyway.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-mysql</artifactId>
                <version>${flyway.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-database-oceanbase</artifactId>
                <version>${flyway.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-database-postgresql</artifactId>
                <version>${flyway.postgresql.version}</version>
            </dependency>
            <!--数据库 end-->

            <!-- office 工具类 start-->
            <dependency>
                <groupId>cn.idev.excel</groupId>
                <artifactId>fastexcel</artifactId>
                <version>${fastexcel.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tika</groupId>
                <artifactId>tika-core</artifactId>
                <version>${tika-core.verison}</version>
            </dependency>
            <!-- office 工具类 end-->

            <!--mapstruct start-->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <!--mapstruct end-->

            <!--工作流程 flowable-->
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter</artifactId>
                <version>${flowable.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-rest</artifactId>
                <version>${flowable.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-json-converter</artifactId>
                <version>${flowable-json-converter.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <!-- 生成sources源码包的插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.2.1</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- 多模块version统一的插件 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.4.1</version>
                <configuration>
                    <!-- 指示生成的扁平化 POM 是否应设置为当前项目的 POM 文件的标志 -->
                    <updatePomFile>true</updatePomFile>
                    <!-- 仅解析变量 revision、sha1 和 changelist。保留其他一切 -->
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <!-- enable flattening -->
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <!-- ensure proper cleanup -->
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>maven-releases</id>
            <name>nexus local</name>
            <url>http://192.168.77.116:6090/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <name>nexus local</name>
            <url>http://192.168.77.116:6090/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>maven-public</id>
            <name>maven-public</name>
            <url>http://192.168.77.116:6090/repository/maven-public/</url>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
        </repository>
    </repositories>
</project>