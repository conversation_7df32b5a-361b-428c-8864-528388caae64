package com.allin.view.code.generator.util;

import java.sql.Types;
import java.util.HashMap;
import java.util.Map;

public class JdbcTypeConverter  {

    private static final Map<Integer, String> JDBC_TO_JAVA_TYPE_MAP = new HashMap<>();

    static {
        JDBC_TO_JAVA_TYPE_MAP.put(Types.ARRAY, "java.sql.Array");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.BIT, "Boolean");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.TINYINT, "Integer");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.SMALLINT, "Short");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.INTEGER, "Integer");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.BIGINT, "Long");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.FLOAT, "Float");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.REAL, "Float");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.DOUBLE, "Double");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.NUMERIC, "java.math.BigDecimal");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.DECIMAL, "java.math.BigDecimal");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.CHAR, "String");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.VARCHAR, "String");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.LONGVARCHAR, "String");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.DATE, "java.time.LocalDate");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.TIME, "java.time.LocalTime");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.TIMESTAMP, "java.time.LocalDateTime");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.BINARY, "byte[]");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.VARBINARY, "byte[]");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.LONGVARBINARY, "byte[]");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.NULL, "null");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.OTHER, "Object");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.BLOB, "byte[]");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.CLOB, "java.sql.Clob");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.BOOLEAN, "Boolean");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.NVARCHAR, "String");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.NCHAR, "String");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.NCLOB, "java.sql.NClob");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.STRUCT, "java.sql.Struct");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.JAVA_OBJECT, "Object");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.DISTINCT, "java.sql.Ref");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.REF, "java.sql.Ref");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.DATALINK, "java.net.URL");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.ROWID, "java.sql.RowId");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.LONGNVARCHAR, "String");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.TIME_WITH_TIMEZONE, "java.sql.Time");
        JDBC_TO_JAVA_TYPE_MAP.put(Types.TIMESTAMP_WITH_TIMEZONE, "java.sql.Timestamp");
    }

    public static String getJavaType(int jdbcType) {
        return JDBC_TO_JAVA_TYPE_MAP.getOrDefault(jdbcType, "Object");
    }


}
