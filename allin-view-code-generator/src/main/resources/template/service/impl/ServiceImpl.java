package ${package_service_impl};

import ${package_mapper}.${Table}Mapper;
import ${package_service}.${Table}Service;
import ${package_pojo}.dto.Add${Table}Dto;
import ${package_pojo}.dto.Edit${Table}Dto;
import ${package_pojo}.entity.${Table};
import ${package_pojo}.query.${Table}Query;
import ${package_pojo}.vo.${Table}Vo;
import cn.hutool.core.bean.BeanUtil;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.base.exception.service.ValidationFailureException;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.time.LocalDateTime;
import com.allin.view.config.i18n.I18nMessageUtil;
import com.allin.view.base.constant.I18nConstants;
/**
 * @description 针对表【${TableName}(${TableInfo})】的数据库操作Service实现
 * <AUTHOR>
 * @since ${date}
 */
@Service
public class ${Table}ServiceImpl extends ServiceImpl<${Table}Mapper, ${Table}>
        implements ${Table}Service{

    @Override
    public boolean create${tableNameUnderline}(Add${Table}Dto add${Table}Dto) {
        ${Table} ${table} = new ${Table}();
        BeanUtils.copyProperties(add${Table}Dto, ${table});
        return save(${table});
    }

    @Override
    public boolean update${tableNameUnderline}(Edit${Table}Dto edit${Table}Dto) {
        String id = edit${Table}Dto.getId();
        ${Table} old = getById(id);
        if (old == null) {
            throw new ValidationFailureException(I18nMessageUtil.getMessage(I18nConstants.DATA_NOT_EXIST));
        }
        BeanUtils.copyProperties(edit${Table}Dto, old);
        return updateById(old);
    }


    @Override
    public boolean delete${tableNameUnderline}(String id) {
        return lambdaUpdate().eq(${Table}::getId, id).set(${Table}::getDeleted, true)
                .set(${Table}::getUpdatedTime, LocalDateTime.now())
                .set(${Table}::getUpdatedBy, SecurityContextHolder.getLoginUser().getUserId())
                .update();
    }


    @Override
    public void get${tableNameUnderline}Page(Page<${Table}Vo> page, ${Table}Query ${table}Query) {
        baseMapper.get${tableNameUnderline}Page(page, ${table}Query);
    }



    @Override
    public ${Table}Vo get${tableNameUnderline}(String id) {
        ${Table} ${table} = getById(id);
        ${Table}Vo ${table}Vo = new ${Table}Vo();
        BeanUtils.copyProperties(${table}, ${table}Vo);
        return ${table}Vo;
    }
}




