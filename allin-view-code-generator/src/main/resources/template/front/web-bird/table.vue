
<template>
  <el-card>
    <!-- 上面搜索功能 -->
    <el-form :model="queryParams" ref="queryRef" v-show="showSearch" :inline="true">
      <el-form-item label="参数名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入参数名称" clearable style="width: 240px" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" type="primary" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
  </el-card>
  <el-card>
    <el-row :gutter="10" justify="end" class="mb-8">
      <el-col :span="1.5">
        <el-button type="primary" icon="Plus" @click="handleAdd">新增</el-button>
      </el-col>
    </el-row>
    <!-- 表格数据 -->
    <el-table stripe v-loading="loading" :data="tableList">
      <el-table-column label="名称" prop="name" align="center" :show-overflow-tooltip="true" min-width="200" />
      <#list models as model>
      <#if model.name != 'deleted' && model.name != 'updatedBy' && model.name != 'updatedTime' && model.name != 'createdBy' && model.name != 'createdTime'>
          <el-table-column label="${model.desc!""}" prop="${model.name}" align="center" :show-overflow-tooltip="true" min-width="200" />
        </#if>
      </#list>
      <el-table-column label="操作" align="center" min-width="200" fixed="right">
        <template #default="scope">
          <el-button size="small" type="warning" icon="Edit" @click="handleUpdate(scope.row)">修改
          </el-button>
          <el-button size="small" type="danger" icon="Delete" @click="handleDelete(scope.row)">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
   <Pagination v-show="total > 0" :total="total" v-model:page="queryParams.current"
              v-model:limit="queryParams.size" @pagination="get${Table}List" />
    <${table_horizontal}-dialog @refresh-data="get${Table}List" ref="formRef"></${table_horizontal}-dialog>
  </el-card>
</template>
<script setup>

import { getCurrentInstance, reactive, ref } from 'vue'
const { proxy } = getCurrentInstance();
const loading = ref(true);
//分页的总共数据
const total = ref(0);
// ${TableInfo}列表渲染数据
const tableList = ref([]);
const formRef = ref()
// 查询参数
const queryParams = reactive({
  current: 1,
  size: 10
})
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.current = 1;
  get${Table}List();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal.confirm('您确定要删除数据?').then(function () {
    return delete${Table}(row.id);
  }).then((res) => {
    if (res.code == 200) {
      get${Table}List();
      proxy.$modal.notifySuccess("删除成功");
    }
  }).catch(() => { });
}
/** 添加${TableInfo} */
function handleAdd() {
  formRef.handleOpen()
}
/** 更新${TableInfo} */
function handleUpdate(id) {
  formRef.handleOpen(id)
}
/**分页查询${TableInfo}*/
async function get${Table}List() {
  loading.value = true;
  let { data } = await  page${Table}(queryParams);
  loading.value = false;
  tableList.value = data.records;
  total.value = data.total;
}
</script>
<style scoped lang="scss">

</style>