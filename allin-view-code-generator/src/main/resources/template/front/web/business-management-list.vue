<template>
  <div
      :class="['flex', 'flex-col', 'basic-gap', 'h-full', 'theme-block-large']"
  >
    <BreadCrumb :buttons="buttons" />

    <AllinQueryForm v-bind="queryProps" @query="onQuery" />

    <WrapperTable :data="tableData" :columns="columns" />

    <AllinWrapperPagination
        v-bind="paginationProps"
        @size-change="getRemoteData"
        @current-change="getRemoteData"
    />

    <${Table}Dialog
        @register="formRegister"
        @refresh-data="getRemoteData"
    ></${Table}Dialog>
  </div>
</template>

<script lang="ts" setup>
import { useDialog, useI18n, usePaginationProps } from 'hooks'
import { confirmDataPopup, deleteDataPopup, exportExcel, to } from 'utils'
import ${Table}Dialog from './components/${table_horizontal}-dialog.vue'
import type { RenderButtonProps } from '@/components/bread-crumb'
import type { RenderColumnProps } from '@/components/wrapper-table'
import {
  delete${Table},
  type ${Table}QO,
  type ${Table}VO,
  export${Table},
  get${Table}List
} from `@/${frontApiPath}`

const { t } = useI18n()
// 表格列
const columns: RenderColumnProps[] = [
  <#list models as model>
    <#if model.name != 'deleted' && model.name != 'updatedBy' && model.name != 'updatedTime' && model.name != 'createdBy' && model.name != 'createdTime'>
      {
        label: t('router.allinSecurity.${table}.${model.name}'),
        prop: '${model.name}',
        minWidth: 220
      },
    </#if>
  </#list>
  {
    type: 'op',
    buttons: [
      {
        permission: 'view-item',
        type: 'success',
        onClick({ row }) {
          onView(row)
        }
      },
      {
        permission: 'edit-item',
        type: 'warning',
        onClick({ row }) {
          onEdit(row)
        }
      },
      {
        permission: 'delete-item',
        type: 'danger',
        onClick({ row }) {
          deleteDataPopup(async () => {
            await onDelete(row.id)
          })
        }
      }
    ]
  }
]
const [paginationProps, paginationParams] = usePaginationProps()
const [formRegister, { openPopup: openDialog }] = useDialog()
const buttons: RenderButtonProps[] = [
  {
    permission: 'export-excel',
    type: 'success',
    onClick() {
      onExport()
    }
  },
  {
    permission: 'add-item',
    type: 'warning',
    onClick() {
      onAdd()
    }
  }
]
// 查询${TableInfo}参数
const queryProps = computed(() => ({
  formItemDescription: [
    <#list models as model>
      <#if model.name != 'deleted' && model.name != 'updatedBy' && model.name != 'updatedTime' && model.name != 'createdBy' && model.name != 'createdTime'>
        {
          label: t('router.allinSecurity.${table}.${model.name}'),
          prop: '${model.name}',
          elementDescription: {
            type: 'WrapperInput',
            modelName: '${model.name}',
            props: { clearable: true }
          }
        },
      </#if>
    </#list>
  ]
}))
// 查询${TableInfo}列表
const tableData = ref<${Table}VO[]>([])
const queryParams = ref<AnyObj>({})
// 获取表格数据
async function getRemoteData() {
  const { data } = await get${Table}List({
    ...toValue(paginationParams),
    ...toValue(queryParams)
  })
  tableData.value = data.records
  paginationProps.total = data.total
}
// 新增${TableInfo}
async function onAdd() {
  openDialog({
    type: 'add'
  })
}
// 查看${TableInfo}
async function onView(row: ${Table}VO) {
  openDialog({
    id: row.id,
    type: 'view'
  })
}
// 编辑${TableInfo}
async function onEdit(row: ${Table}VO) {
  openDialog({
    id: row.id,
    type: 'edit'
  })
}
// 删除${TableInfo}
async function onDelete(id: string) {
  const rs = await delete${Table}(id)
  if (rs.code === 200) {
    await getRemoteData()
  }
}
// 导出${TableInfo}
function onExport() {
  exportExcel(() => export${Table}({
    ...toValue(paginationParams),
    ...toValue(queryParams)
  }))
}

function onQuery(params: ${Table}QO) {
  queryParams.value = params
  getRemoteData()
}
// 获取表格数据
getRemoteData()
</script>

<style lang="scss" scoped></style>
