package ${package_pojo}.vo;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.*;
import cn.idev.excel.enums.poi.HorizontalAlignmentEnum;
import cn.idev.excel.enums.poi.VerticalAlignmentEnum;
import lombok.Data;
import org.apache.poi.ss.usermodel.Font;
import cn.idev.excel.annotation.write.style.*;
import cn.idev.excel.enums.poi.HorizontalAlignmentEnum;
import cn.idev.excel.enums.poi.VerticalAlignmentEnum;

/**
 * ${Table} ${TableInfo}错误日志Vo
 * <AUTHOR>
 * @since ${date}
 */
@Data
@ColumnWidth(50)
@HeadFontStyle(fontHeightInPoints = 12)
@HeadRowHeight(27)
@ContentRowHeight(27)
@ContentFontStyle(fontHeightInPoints = 12)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER,
        verticalAlignment = VerticalAlignmentEnum.CENTER)
public class ${Table}ErrorVo extends ${Table}ExcelVo {
    /**
     * 错误信息
     */
    @ExcelProperty(value = "错误信息", index = -1)
    @HeadFontStyle(fontHeightInPoints = 12, color = Font.COLOR_RED)
    private String errorMsg;
}
