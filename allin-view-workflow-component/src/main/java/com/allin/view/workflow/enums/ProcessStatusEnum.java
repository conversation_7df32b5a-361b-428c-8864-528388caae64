package com.allin.view.workflow.enums;

import lombok.Getter;

/**
 * 流程状态
 *
 * <AUTHOR>
 * @date 2023/8/15
 */
@Getter
public enum ProcessStatusEnum {

    CANCEL(0, "已撤回"),
    INIT(1, "待审核"),
    PROCESS(2, "审核中"),
    COMPLETED(3, "已完成"),
    INVALID(4, "已作废"),
    DELETE(5, "已删除"),
    SUSPEND(6, "已暂停"),
    REJECT(7, "已驳回");


    private final String desc;

    private final Integer code;

    ProcessStatusEnum(Integer code, String desc) {
        this.desc = desc;
        this.code = code;
    }


}
