package com.allin.view.workflow.pojo.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * 启动流程实例请求类（提交申请)
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InitProcessDto implements Serializable {


    /**
     * 流程key
     */
    @NotBlank(message="processKey不能为空")
    private String processKey;

    /**
     * 业务唯一key
     */
    private String businessKey;

    /**
     * 流程变量
     */
    private Map<String, Object> variables;

    /**
     * 发起人
     */
    private String startUserId;

}
