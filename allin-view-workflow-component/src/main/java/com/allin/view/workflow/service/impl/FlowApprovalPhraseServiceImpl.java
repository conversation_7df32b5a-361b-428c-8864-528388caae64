package com.allin.view.workflow.service.impl;

import com.allin.view.workflow.mapper.FlowApprovalPhraseMapper;
import com.allin.view.workflow.pojo.entity.FlowApprovalPhrase;
import com.allin.view.workflow.service.FlowApprovalPhraseService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【flow_approval_phrase(审批常用短语表)】的数据库操作Service实现
* @date 2023-08-17 10:57:19
*/
@Service
public class FlowApprovalPhraseServiceImpl extends ServiceImpl<FlowApprovalPhraseMapper, FlowApprovalPhrase>
    implements FlowApprovalPhraseService {

}




