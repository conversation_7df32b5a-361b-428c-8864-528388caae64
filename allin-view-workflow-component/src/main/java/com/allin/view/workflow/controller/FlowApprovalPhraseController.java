package com.allin.view.workflow.controller;

import com.allin.view.base.domain.Result;
import com.allin.view.workflow.pojo.dto.AddFlowApprovalPhraseDto;
import com.allin.view.workflow.pojo.entity.FlowApprovalPhrase;
import com.allin.view.workflow.service.FlowApprovalPhraseService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 审批常用短语接口
 *
 * <AUTHOR>
 * @date 2023/8/14
 */
@RequestMapping("/flow_approval_phrase")
@RestController
public class FlowApprovalPhraseController {

    private final FlowApprovalPhraseService flowApprovalPhraseService;


    public FlowApprovalPhraseController(FlowApprovalPhraseService flowApprovalPhraseService) {
        this.flowApprovalPhraseService = flowApprovalPhraseService;
    }

    /**
     * 获取常用短语列表
     */
    @GetMapping("/list")
    public Result<List<FlowApprovalPhrase>> list() {
        final List<FlowApprovalPhrase> list = flowApprovalPhraseService.list();
        return Result.ok(list);
    }

    /**
     * 新增常用短语
     */
    @PostMapping
    public Result<String> add(@RequestBody @Validated AddFlowApprovalPhraseDto addFlowApprovalPhraseDto) {
        final FlowApprovalPhrase flowApprovalPhrase = new FlowApprovalPhrase();
        flowApprovalPhrase.setPhrase(addFlowApprovalPhraseDto.getPhrase());
        flowApprovalPhrase.setCreateTime(LocalDateTime.now());
        return flowApprovalPhraseService.save(flowApprovalPhrase) ? Result.ok() : Result.fail();
    }

    /**
     * 删除常用短语
     */
    @DeleteMapping("/{id}")
    public Result<String> delete(@PathVariable Long id) {
        return flowApprovalPhraseService.removeById(id) ? Result.ok() : Result.fail();
    }
}
