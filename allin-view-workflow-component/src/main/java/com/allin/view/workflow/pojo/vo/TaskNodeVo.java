package com.allin.view.workflow.pojo.vo;

import lombok.Data;

import java.util.Map;

/**
 * 任务节点
 *
 * <AUTHOR>
 * @date 2023/8/4
 */
@Data
public class TaskNodeVo {
    /**
     * 任务节点定义key
     */
    private String taskDefinitionKey;

    /**
     * 节点名称
     */
    private String name;

    /**
     * 文档
     */
    private String documentation;

    /**
     * 节点处理对象
     */
    private String assignee;

    /**
     * 其他附加属性
     */
    private Map<String, Object> extensionElements;

    /**
     * 表单项
     */
    private Map<String, Object> formProperties;
}
