package com.allin.view.workflow.service.impl;

import cn.hutool.core.util.StrUtil;
import com.allin.view.base.exception.service.ValidationFailureException;
import com.allin.view.workflow.exception.WorkflowErrorCodeConstants;
import com.allin.view.workflow.pojo.vo.BusinessDetailVo;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.allin.view.workflow.pojo.entity.FlwBusinessDefinition;
import com.allin.view.workflow.service.FlwBusinessDefinitionService;
import com.allin.view.workflow.mapper.FlwBusinessDefinitionMapper;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.HistoryService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【flw_business_definition(业务模块流程定义)】的数据库操作Service实现
 * @createDate 2024-03-06 09:39:36
 */
@Service
@Slf4j
public class FlwBusinessDefinitionServiceImpl extends ServiceImpl<FlwBusinessDefinitionMapper, FlwBusinessDefinition>
        implements FlwBusinessDefinitionService {

    private final HistoryService historyService;

    public FlwBusinessDefinitionServiceImpl(HistoryService historyService) {
        this.historyService = historyService;
    }

    @Override
    public void updateBusinessStatus(String processKey, String businessKey, int status) {
        FlwBusinessDefinition flwBusinessDefinition = lambdaQuery().eq(FlwBusinessDefinition::getProcessDefinitionKey, processKey)
                .eq(FlwBusinessDefinition::getDeleted, false).one();
        if (flwBusinessDefinition == null) {
            throw new ValidationFailureException(WorkflowErrorCodeConstants.PROCESS_DEFINITION_NOT_EXIST.getCode(),
                    WorkflowErrorCodeConstants.PROCESS_DEFINITION_NOT_EXIST.getMsg()
            );
        }
        try {
            getBaseMapper().updateBusinessStatus(
                    flwBusinessDefinition.getBusinessTableName(),
                    flwBusinessDefinition.getBusinessKeyField(),
                    businessKey,
                    flwBusinessDefinition.getBusinessStatusField(),
                    status
            );
        } catch (Exception e) {
            log.error("流程配置错误， 请检查配置是否正确...");
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public BusinessDetailVo getBusinessDetail(String businessKey, String processInstanceId) {
        if (StrUtil.isEmpty(businessKey) || StrUtil.isEmpty(processInstanceId)) {
            throw new ValidationFailureException(WorkflowErrorCodeConstants.PARAMETER_ERROR.getCode(),
                    WorkflowErrorCodeConstants.PARAMETER_ERROR.getMsg());
        }
        if (StrUtil.isNotEmpty(businessKey)) {
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceBusinessKey(businessKey).singleResult();
            return getBusinessDetail(businessKey, processInstanceId, checkFlow(historicProcessInstance));
        } else {
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
            businessKey = historicProcessInstance.getBusinessKey();
            return getBusinessDetail(businessKey, processInstanceId, checkFlow(historicProcessInstance));
        }
    }

    private FlwBusinessDefinition checkFlow(HistoricProcessInstance historicProcessInstance) {
        if (historicProcessInstance == null) {
            throw new ValidationFailureException(WorkflowErrorCodeConstants.PROCESS_INSTANCE_NOT_EXIST.getCode(),
                    WorkflowErrorCodeConstants.PROCESS_INSTANCE_NOT_EXIST.getMsg());
        }
        String processKey = historicProcessInstance.getProcessDefinitionKey();
        FlwBusinessDefinition flwBusinessDefinition = lambdaQuery().eq(FlwBusinessDefinition::getProcessDefinitionKey, processKey)
                .eq(FlwBusinessDefinition::getDeleted, false).one();
        if (flwBusinessDefinition == null) {
            throw new ValidationFailureException(WorkflowErrorCodeConstants.PROCESS_DEFINITION_NOT_EXIST.getCode(),
                    WorkflowErrorCodeConstants.PROCESS_DEFINITION_NOT_EXIST.getMsg());
        }
        return flwBusinessDefinition;
    }

    private BusinessDetailVo getBusinessDetail(String businessKey, String processInstanceId, FlwBusinessDefinition flwBusinessDefinition) {
        String businessId = getBaseMapper().queryBusinessId(flwBusinessDefinition.getBusinessTableName(), flwBusinessDefinition.getBusinessKeyField(), businessKey);
        if (StrUtil.isEmpty(businessId)) {
            throw new ValidationFailureException(WorkflowErrorCodeConstants.BUSINESS_ID_NOT_EXIST.getCode(),
                    WorkflowErrorCodeConstants.BUSINESS_ID_NOT_EXIST.getMsg());
        }
        BusinessDetailVo businessDetailVo = new BusinessDetailVo();
        businessDetailVo.setBusinessId(businessId);
        businessDetailVo.setBusinessKey(businessKey);
        businessDetailVo.setFormKey(flwBusinessDefinition.getFormKey());
        businessDetailVo.setProcessInstanceId(processInstanceId);
        return businessDetailVo;
    }
}




