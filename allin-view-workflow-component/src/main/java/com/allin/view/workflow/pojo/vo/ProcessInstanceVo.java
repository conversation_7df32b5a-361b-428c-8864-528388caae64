package com.allin.view.workflow.pojo.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/4
 */
@Data
public class ProcessInstanceVo {

    /**
     * 流程实例id
     */
    private String processInstanceId;

    /**
     * 流程名称
     */
    private String name;

    /**
     * 流程定义key
     */
    private String processDefinitionKey;

    /**
     * 流程定义版本
     */
    private Integer processDefinitionVersion;

    /**
     * 发起人的唯一标识
     */
    private String startedBy;
    /**
     * 发起人的姓名
     */
    private String fullName;

    /**
     * 状态
     */
    private Boolean state;

    /**
     * 状态文本
     */
    private String stateText;

    /**
     * 业务唯一标识id
     */
    private String businessKey;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 当前环节信息
     */
    private List<TaskInstanceVo> currNodeInfo;
}
