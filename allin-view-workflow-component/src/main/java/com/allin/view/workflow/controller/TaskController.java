package com.allin.view.workflow.controller;

import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.Result;
import com.allin.view.workflow.pojo.dto.TaskBackDto;
import com.allin.view.workflow.pojo.dto.TaskCompleteDto;
import com.allin.view.workflow.pojo.dto.TurnTaskDto;
import com.allin.view.workflow.pojo.qo.CompletedTaskPageQuery;
import com.allin.view.workflow.pojo.qo.ProcInstPageQuery;
import com.allin.view.workflow.pojo.qo.TodoTaskPageQuery;
import com.allin.view.workflow.pojo.vo.CompleteTaskVo;
import com.allin.view.workflow.pojo.vo.ProcessInstanceVo;
import com.allin.view.workflow.pojo.vo.TaskNodeVo;
import com.allin.view.workflow.pojo.vo.ToDoTaskVo;
import com.allin.view.workflow.service.ITaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 任务控制器
 * <AUTHOR>
 */
@Validated
@RestController
@Slf4j
@RequestMapping("task")
public class TaskController {


    private final ITaskService taskService;

    public TaskController(ITaskService taskService) {
        this.taskService = taskService;
    }

    /**
     * 查询待办任务
     */
    @GetMapping("/page/todo")
    public Result<PageData<ToDoTaskVo>> getTodoTask(TodoTaskPageQuery req) {
        return Result.ok(taskService.getTodoTask(req));
    }

    /**
     * 查询已完成任务
     */
    @GetMapping("/page/complete")
    public Result<PageData<CompleteTaskVo>> getCompleteTask(CompletedTaskPageQuery req) {
        return Result.ok(taskService.getCompletedTask(req));
    }

    /**
     * 查询我发起的任务
     */
    @GetMapping("/page/start")
    public Result<PageData<ProcessInstanceVo>> getStartTask(ProcInstPageQuery req) {
        return Result.ok(taskService.getStartTask(req));
    }

    /**
     * 获取目标节点（下一个节点）
     *
     * @param taskId 任务id
     */
    @GetMapping("/next/node")
    public Result<List<TaskNodeVo>> getNextNodeInfo(@RequestParam String taskId) {
        return Result.ok(taskService.getNextNodeInfo(taskId));
    }

    /**
     * 获取上一个节点信息
     *
     * @param taskId 任务id
     */
    @GetMapping("/pre/node")
    public Result<List<TaskNodeVo>> getPreNodeInfo(@RequestParam String taskId) {
        return Result.ok(taskService.getPreNodeInfo(taskId));
    }

    /**
     * 完成当前任务
     */
    @PostMapping("/complete")
    Result<String> completeTask(@RequestBody TaskCompleteDto req) {
        boolean completed = taskService.completeTask(req);
        if (completed) {
            return Result.ok("任务完成成功");
        }
        return Result.fail("任务完成失败");
    }

    /**
     * 转办任务给别人办理
     */
    @PostMapping("/turn")
    Result<String> turnTask(@RequestBody TurnTaskDto turnTask) {
        boolean completed = taskService.turnTask(turnTask);
        if (completed) {
            return Result.ok("任务转办成功");
        }
        return Result.fail("任务转办失败");
    }

    /**
     * 驳回历史节点
     */
    @PostMapping("/back")
    Result<String> backTask(@RequestBody TaskBackDto req) {
        boolean completed = taskService.backTask(req);
        if (completed) {
            return Result.ok("任务驳回成功");
        }
        return Result.fail("任务驳回失败");
    }
}
