package com.allin.view.workflow.service;

import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.Result;
import com.allin.view.workflow.pojo.dto.TaskBackDto;
import com.allin.view.workflow.pojo.dto.TaskCompleteDto;
import com.allin.view.workflow.pojo.dto.TurnTaskDto;
import com.allin.view.workflow.pojo.qo.CompletedTaskPageQuery;
import com.allin.view.workflow.pojo.qo.ProcInstPageQuery;
import com.allin.view.workflow.pojo.qo.TodoTaskPageQuery;
import com.allin.view.workflow.pojo.vo.CompleteTaskVo;
import com.allin.view.workflow.pojo.vo.ProcessInstanceVo;
import com.allin.view.workflow.pojo.vo.TaskNodeVo;
import com.allin.view.workflow.pojo.vo.ToDoTaskVo;

import java.util.List;

public interface ITaskService {
    /**
     * 查询待办任务
     */
    PageData<ToDoTaskVo> getTodoTask(TodoTaskPageQuery req);

    /**
     * 查询已办任务
     */
    PageData<CompleteTaskVo> getCompletedTask(CompletedTaskPageQuery req);

    /**
     * 获取目标节点（下一个节点）
     */
    List<TaskNodeVo> getNextNodeInfo(String taskId);

    /**
     * 获取源节点（上一个节点）
     */
    List<TaskNodeVo> getPreNodeInfo(String taskId);

    /**
     * 完成任务
     */
    boolean completeTask(TaskCompleteDto req);

    /**
     * 转派任务
     */
    boolean turnTask(TurnTaskDto turnTask);

    /**
     * 退回任务
     */
    boolean backTask(TaskBackDto req);

    /**
     * 查询我发起的任务
     */
    PageData<ProcessInstanceVo> getStartTask(ProcInstPageQuery req);
}
