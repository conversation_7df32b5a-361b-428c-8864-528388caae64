
<div class="modal" ng-controller="CreateNewDecisionTableCtrl">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header"><h2>{{'DECISION-TABLE.POPUP.CREATE-TITLE' | translate}}</h2></div>
			<div class="modal-body">
				<p>{{'DECISION-TABLE.POPUP.CREATE-DESCRIPTION' | translate}}</p>
				<div ng-if="model.errorMessage && model.errorMessage.length > 0" class="alert error" style="font-size: 14px; margin-top:20px">
                  <div class="popup-error" style="font-size: 14px">
                    <span class="glyphicon glyphicon-remove-circle"></span>
                    <span>{{model.errorMessage}}</span>
                  </div>
                </div>
				<div class="form-group">
				    <label for="newDecisionTableName">{{'DECISION-TABLE.NAME' | translate}}</label>
				    <input ng-disabled="model.loading" type="text" class="form-control" maxlength="400"
			               id="newDecisionTableName" ng-model="model.decisionTable.name" custom-keys enter-pressed="ok()" auto-focus editor-input-check>
				</div>
				<div class="form-group">
				    <label for="newDecisionTableKey">{{'DECISION-TABLE.KEY' | translate}}</label>
				    <input ng-disabled="model.loading" type="text" class="form-control" maxlength="255"
			               id="newDecisionTableKey" ng-model="model.decisionTable.key" editor-input-check>
				</div>
				<div class="form-group">
				    <label for="newDecisionTableDescription">{{'DECISION-TABLE.DESCRIPTION' | translate}}</label>
					<textarea ng-disabled="model.loading" class="form-control" id="newDecisionTableDescription" rows="5" ng-model="model.decisionTable.description"></textarea>
				</div>
			</div>
			
			<div class="modal-footer">
				<div class="pull-right">
					<button type="button" class="btn btn-sm btn-default" ng-click="cancel()" ng-disabled="model.loading">
						{{'GENERAL.ACTION.CANCEL' | translate}}
					</button>
					<button type="button" class="btn btn-sm btn-default" ng-click="ok()" ng-disabled="model.loading || !model.decisionTable.name || model.decisionTable.name.length == 0 || !model.decisionTable.key || model.decisionTable.key.length == 0">
						{{'DECISION-TABLE.ACTION.CREATE-CONFIRM' | translate}}
					</button>
				</div>
				<div class="loading pull-right" ng-show="model.loading">
					<div class="l1"></div><div class="l2"></div><div class="l2"></div>
				</div>
			</div>
		</div>
	</div>
</div>