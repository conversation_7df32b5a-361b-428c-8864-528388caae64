
<div class="modal" ng-controller="SaveFormCtrl">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header"><h2>{{'FORM.POPUP.SAVE-FORM-TITLE' | translate}}</h2></div>
			<div class="modal-body">
				<p>{{'FORM.POPUP.CREATE-DESCRIPTION' | translate}}</p>
				<div ng-if="saveDialog.errorMessage && saveDialog.errorMessage.length > 0" class="alert error" style="font-size: 14px; margin-top:20px">
                  <div class="popup-error" style="font-size: 14px">
                    <span class="glyphicon glyphicon-remove-circle"></span>
                    <span>{{saveDialog.errorMessage}}</span>
                  </div>
                </div>
				<div class="form-group">
				    <label for="formName">{{'FORM.NAME' | translate}}</label>
				    <input ng-disabled="model.loading" type="text" class="form-control"
			               id="formName" ng-model="saveDialog.name" ui-keypress="{13:'ok()'}" auto-focus editor-input-check>
				</div>
				<div class="form-group">
				    <label for="formKey">{{'FORM.KEY' | translate}}</label>
				    <input ng-disabled="model.loading" type="text" class="form-control"
			               id="formKey" ng-model="saveDialog.formKey" editor-input-check>
				</div>
				<div class="form-group">
				    <label for="formDescription">{{'FORM.DESCRIPTION' | translate}}</label>
					<textarea ng-disabled="model.loading" class="form-control" id="formDescription" rows="5" ng-model="saveDialog.description"></textarea>
				</div>
				<div class="checkbox" ng-show="!error && !error.isConflict">
                    <label>
                        <input type="checkbox" ng-disabled="status.loading" ng-model="saveDialog.newVersion">
                            {{'MODEL.SAVE.NEWVERSION' | translate}}
                    </label>
                </div>
                <div class="form-group" ng-if="saveDialog.newVersion">
                    <label for="commentTextArea">{{'MODEL.SAVE.COMMENT' | translate}}</label>
                    <textarea id="commentTextArea" class="form-control" ng-model="saveDialog.comment" ng-disabled="status.loading"></textarea>
                </div>
			</div>

			<div class="modal-footer">
				<div class="pull-right">
					<button class="btn btn-sm btn-default" ng-click="cancel()" ng-disabled="model.loading" translate>GENERAL.ACTION.CANCEL</button>
					<button class="btn btn-primary" ng-click="saveAndClose()" ng-disabled="status.loading || saveDialog.name.length == 0 || saveDialog.key.length == 0" ng-show="!error && !error.isConflict" translate>ACTION.SAVE-AND-CLOSE</button>
					<button class="btn btn-primary" ng-click="save()" ng-disabled="status.loading || saveDialog.name.length == 0 || saveDialog.key.length == 0" ng-show="!error && !error.isConflict" translate>ACTION.SAVE</button>
				</div>
				<div class="loading pull-right" ng-show="model.loading">
					<div class="l1"></div><div class="l2"></div><div class="l2"></div>
				</div>
			</div>
		</div>
	</div>
</div>
