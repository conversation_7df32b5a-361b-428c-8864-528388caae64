
<div class="modal" ng-controller="CreateNewCaseModelModelCtrl">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header"><h2>{{'CASE.POPUP.CREATE-TITLE' | translate}}</h2></div>
			<div class="modal-body">
				<p>{{'PROCESS.POPUP.CREATE-DESCRIPTION' | translate}}</p>
				<div ng-if="model.errorMessage && model.errorMessage.length > 0" class="alert error" style="font-size: 14px; margin-top:20px">
                  <div class="popup-error" style="font-size: 14px">
                    <span class="glyphicon glyphicon-remove-circle"></span>
                    <span>{{model.errorMessage}}</span>
                  </div>
                </div>
				<div class="form-group">
				    <label for="newCaseModelName">{{'CASE.NAME' | translate}}</label>
				    <input ng-disabled="model.loading" type="text" class="form-control"
			               id="newCaseModelName" ng-model="model.caseModel.name" auto-focus editor-input-check>
				</div>
				<div class="form-group">
				    <label for="newCaseModelKey">{{'CASE.KEY' | translate}}</label>
				    <input ng-disabled="model.loading" type="text" class="form-control"
			               id="newCaseModelKey" ng-model="model.caseModel.key" editor-input-check>
				</div>
				<div class="form-group">
				    <label for="newCaseModelDescription">{{'CASE.DESCRIPTION' | translate}}</label>
					<textarea ng-disabled="model.loading" class="form-control" id="newCaseModelDescription" rows="5" ng-model="model.caseModel.description"></textarea>
				</div>
			</div>
			
			<div class="modal-footer">
				<div class="pull-right">
					<button type="button" class="btn btn-sm btn-default" ng-click="cancel()" ng-disabled="model.loading">
						{{'GENERAL.ACTION.CANCEL' | translate}}
					</button>
					<button type="button" class="btn btn-sm btn-default" ng-click="ok()" ng-disabled="model.loading || !model.caseModel.name || model.caseModel.name.length == 0 || !model.caseModel.key || model.caseModel.key.length == 0">
						{{'PROCESS.ACTION.CREATE-CONFIRM' | translate}}
					</button>
				</div>
				<div class="loading pull-right" ng-show="model.loading">
					<div class="l1"></div><div class="l2"></div><div class="l2"></div>
				</div>
			</div>
		</div>
	</div>
</div>