<div class="modal" tabindex="-1" role="dialog" ng-controller="ValidateModelCtrl">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<h2>{{'MODEL.VALIDATE.TITLE' | translate}}</h2>
			</div>
			<div class="modal-body">
				<div>
					<div class="alert alert-success" role="alert" ng-show="model.errors.length == 0 && !status.loading">No errors detected.</div>
					<div ng-show="errorGrid && !status.loading" class="validationGrid" ui-grid="errorGrid" ui-grid-selection></div>
				</div>
			</div>
			<div class="modal-footer">
				<button type="button" ng-disabled="status.loading" class="btn btn-primary" ng-click="$hide()">Close</button>
			</div>
		</div>
	</div>
</div>