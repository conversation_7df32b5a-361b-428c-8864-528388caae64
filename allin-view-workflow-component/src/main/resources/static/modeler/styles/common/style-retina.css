/* <PERSON><PERSON> tweaks */
@media 	only screen and (-webkit-min-device-pixel-ratio: 1.5),
only screen and (   min--moz-device-pixel-ratio: 1.5),
only screen and (     -o-min-device-pixel-ratio: 3/2),
only screen and (        min-device-pixel-ratio: 1.5),
only screen and (min-resolution: 192dpi) {

    .navbar-header .navbar-brand {
        background-size: 148px 25px;
    }

    .account .google-drive {
        background: transparent url('../../images/google-drive-2x.png') 50% 50% no-repeat;
        background-size: 34px 34px;
    }

    .account .alfresco {
        background: transparent url('../../images/alfresco-2x.png') 50% 50% no-repeat;
        background-size: 34px 34px;
    }

    .account .alfresco-cloud {
        background: transparent url('../../images/alfresco-cloud-2x.png') 50% 50% no-repeat;
        background-size: 34px 34px;
    }
}