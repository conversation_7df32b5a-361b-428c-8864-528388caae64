package com.allin.view.ws.entity;

import cn.hutool.core.date.LocalDateTimeUtil;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 规范 webSocket 前后端通信的最基本的通信格式
 * <pre>{@code
 * {
 *      "type": "message",
 *      "payload": Object,
 *      "sendTime" : "2021-01-01 00:00:00"
 *    }
 * }</pre>
 * <p>
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class WsMessage<T> {

    /**
     * 消息类型
     */
    private String type;

    /**
     * 消息数据
     */
    private T payload;

    /**
     * 消息发送的时间
     */
    private String sendTime = LocalDateTimeUtil.formatNormal(LocalDateTime.now());


    public static <T> WsMessage<T> of(String type, T payload) {
        WsMessage<T> message = new WsMessage<>();
        message.setType(type);
        message.setPayload(payload);
        return message;
    }

}
