<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>allin-view</artifactId>
        <groupId>com.allin</groupId>
        <version>${revision}</version>
    </parent>

    <artifactId>allin-view-auth</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>allin-view-auth</name>
    <modules>
        <module>allin-view-auth-api</module>
        <module>allin-view-auth-service</module>
    </modules>
    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>
</project>
