package com.allin.view.auth.interceptor;

import cn.hutool.core.util.StrUtil;
import com.allin.view.auth.api.ThirdPartyTokenApi;
import com.allin.view.auth.constant.SecurityContextConstants;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.auth.interceptor.annotation.ThirdPartyApi;
import com.allin.view.auth.pojo.vo.ThirdPartyTokenVo;
import com.allin.view.auth.utils.InterceptorUtils;
import com.allin.view.auth.utils.TokenValidateUtils;
import com.allin.view.base.domain.Result;
import com.allin.view.base.utils.api.ApiUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import java.io.IOException;

/**
 * 第三方服务token请求认证拦截器，会校验token并将第三方服务数据封装到线程变量中方便获取
 *
 * <AUTHOR>
 */
public class ThirdPartyTokenInterceptor implements HandlerInterceptor {

    private final ThirdPartyTokenApi thirdPartyTokenApi;

    public ThirdPartyTokenInterceptor(ThirdPartyTokenApi thirdPartyTokenApi) {
        this.thirdPartyTokenApi = thirdPartyTokenApi;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
                                Exception ex) {
        SecurityContextHolder.remove();
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws IOException {
        // 如果不是映射到方法，直接通过
        if (!(handler instanceof HandlerMethod requestMethod)) {
            return true;
        }

        // OPTIONS请求直接放行
        if (InterceptorUtils.isOptionsRequest(request)) {
            response.setStatus(HttpServletResponse.SC_OK);
            return true;
        }

        if (isValidate(requestMethod)) {
            // Token校验
            return validateToken(request, response);
        }

        return true;
    }

    /**
     * 检查请求是否需要第三方令牌校验，类或方法上标注了{@link ThirdPartyApi}注解
     */
    private boolean isValidate(HandlerMethod requestMethod) {
        // 检查类或方法是否标注了白名单注解
        return requestMethod.getBeanType().isAnnotationPresent(ThirdPartyApi.class)
                || requestMethod.hasMethodAnnotation(ThirdPartyApi.class);
    }

    /**
     * 验证Token的有效性
     * 验证通过后会将第三方令牌 ServiceTokenVo存入 SecurityContextHolder
     *
     * @param request  HTTP请求对象
     * @param response HTTP响应对象
     * @return 如果Token验证通过返回true，否则返回false
     * @throws IOException 写入响应时可能发生IO异常
     */
    private boolean validateToken(HttpServletRequest request, HttpServletResponse response) throws IOException {
        final String token = TokenValidateUtils.getThirdPartyToken(request);
        if (StrUtil.isBlank(token)) {
            return true;
        }

        final Result<ThirdPartyTokenVo> check = ApiUtils.getResult(() -> thirdPartyTokenApi.check(token, request.getRequestURI()));
        if (check.isOk() && check.getData() != null) {
            // 将第三方用户信息放入线程上下文中
            SecurityContextHolder.set(SecurityContextConstants.THIRD_PARTY_INFO, check.getData());
            // 设置项目ID到上下文
            SecurityContextHolder.setProjectId(check.getData().getProjectId());
            return true;
        } else {
            InterceptorUtils.writerResponse(response, check.getMessage());
            return false;
        }
    }
}
