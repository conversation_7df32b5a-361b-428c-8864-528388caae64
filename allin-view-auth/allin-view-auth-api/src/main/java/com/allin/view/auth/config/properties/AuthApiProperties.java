package com.allin.view.auth.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashSet;
import java.util.Set;

/**
 * auth 参数配置
 *
 * <AUTHOR>
 * @date 2024/2/28
 */
@Data
@ConfigurationProperties(prefix = "allin.view.auth")
public class AuthApiProperties {

    /**
     * 开启认证, 该属性对 auth 服务本身无效
     */
    private boolean enable = true;

    /**
     * 是否允许同一个账号同端多个设备同时在线
     */
    private boolean allowMultipleLogin = false;

    /**
     * 认证中心服务地址, 该属性对 auth 服务本身无效
     */
    private String url;

    /**
     * 本服务ip, 该属性对 auth 服务本身无效
     * 可以通过指定该属性来解决通过工具可能获取到的网卡 IP 不对的问题
     */
    private String serviceIp;

    /**
     * api白名单配置，非白名单的接口需要进行token验证, 没配置则不生效
     */
    private Set<String> apiWhites = new HashSet<>();

    /**
     * ip白名单配置, 非白名单的ip请求会被拦截, 没配置则不生效
     * 例如：192.168.1.*
     */
    private Set<String> ipWhites = new HashSet<>();

    /**
     * 密码重置周期(天)
     */
    private int passwordResetInterval = 90;

    /**
     * 是否显示水印
     */
    private boolean showWaterMark = false;
}
