package com.allin.view.auth.interceptor;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.util.StrUtil;
import com.allin.view.auth.api.UserPermissionApi;
import com.allin.view.auth.config.properties.AuthApiProperties;
import com.allin.view.auth.constant.SecurityContextConstants;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.auth.permission.RequiresPermission;
import com.allin.view.auth.pojo.dto.JwtToken;
import com.allin.view.auth.pojo.vo.ProjectPermissionVo;
import com.allin.view.auth.utils.InterceptorUtils;
import com.allin.view.base.domain.Result;
import com.allin.view.base.i18n.I18nUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.lang.NonNull;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import java.lang.reflect.Method;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;

/**
 * 权限拦截器
 *
 * <AUTHOR>
 */
public class PermissionInterceptor implements HandlerInterceptor {

    private final UserPermissionApi userPermissionApi;

    private final AuthApiProperties authApiProperties;

    /**
     * 权限缓存 30秒
     */
    private final TimedCache<String, ProjectPermissionVo> permissionCacheMap = CacheUtil.newTimedCache(60000, 30000);

    public PermissionInterceptor(UserPermissionApi userPermissionApi, AuthApiProperties authApiProperties) {
        this.userPermissionApi = userPermissionApi;
        this.authApiProperties = authApiProperties;
    }

    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler) throws Exception {
        // OPTIONS 请求直接放行
        if (InterceptorUtils.isOptionsRequest(request) || InterceptorUtils.isInternalService(request)) {
            return true;
        }

        // 如果不是映射到方法，直接通过
        if (!(handler instanceof HandlerMethod requestMethod)) {
            return true;
        }

        // 第三方令牌校验已通过不执行校验
        if (SecurityContextHolder.exist(SecurityContextConstants.API_KEY_INFO)) {
            return true;
        }

        // 白名单请求校验
        if (InterceptorUtils.isWhiteListRequest(request, requestMethod, authApiProperties.getApiWhites())) {
            return true;
        }

        Method method = requestMethod.getMethod();

        // 获取方法上的RequiresPermission注解
        RequiresPermission permission = method.getAnnotation(RequiresPermission.class);
        if (permission != null) {
            // 获取注解中的权限字符串
            String permissionStr = permission.value();

            // 检查权限
            boolean isValid = validPermission(permissionStr);

            if (!isValid) {
                // 没有权限时可以返回403或者其他处理
                return InterceptorUtils.writerResponse(HttpServletResponse.SC_FORBIDDEN, response, I18nUtil.getMessage("auth.not.permission"));
            }
        }

        return true;  // 如果没有注解或者权限匹配则放行
    }

    private boolean validPermission(String permission) {
        final Optional<JwtToken> jwtTokenOpt = SecurityContextHolder.tryGetLoginUser();
        final Optional<String> projectIdOpt = SecurityContextHolder.tryGetProjectId();

        if (jwtTokenOpt.isEmpty() || projectIdOpt.isEmpty()) {
            return true;
        }

        // 校验用户是不是超级管理员
        final JwtToken jwtToken = jwtTokenOpt.get();

        Boolean isSuperAdmin = jwtToken.getIs_super_admin();
        if (isSuperAdmin) {
            return true;  // 超级管理员跳过权限检查
        }

        // 获取用户权限
        String userId = jwtToken.getUser_id();
        String projectId = projectIdOpt.get();

        // 从缓存中获取用户权限
        ProjectPermissionVo permissionVo = permissionCacheMap.get(userId + ":" + projectId, false);

        if (permissionVo == null) {
            Result<ProjectPermissionVo> projectPermission = userPermissionApi.getByProject(userId, projectId);
            if (projectPermission.isOk()) {
                permissionVo = projectPermission.getData();
                permissionCacheMap.put(userId + ":" + projectId, permissionVo);
            }
        }

        if (permissionVo == null) {
            return false;
        }

        // 用 Set 进行权限判断提高效率
        Set<String> permissionSet = new HashSet<>(StrUtil.split(permissionVo.getPermissions(), ","));
        if (permissionSet.contains("*")) {
            return true;
        }
        return permissionSet.contains(permission);
    }
}