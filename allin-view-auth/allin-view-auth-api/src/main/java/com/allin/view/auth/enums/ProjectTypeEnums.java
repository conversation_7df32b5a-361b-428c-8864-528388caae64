package com.allin.view.auth.enums;

import com.allin.view.base.enums.base.IEnums;

/**
 * 项目类型(围界: allin-security 鸟击：allin-bird 跑道：allin-runaway AI-视频持久监控: allin-ai-detection 管理中心: allin-management-center)
 */
public enum ProjectTypeEnums implements IEnums {

    ALLIN_MANAGEMENT_CENTER("allin-management-center", "管理中心"),
    ALL_IN_SECURITY("allin-security", "围界防入侵系统"),
    ALL_IN_BIRD("allin-bird", "鸟击防范系统"),
    ALL_IN_RUNAWAY("allin-runaway", "跑道防入侵系统"),
    ALL_IN_AI_DETECTION("allin-ai-detection", "AI-视频持久监控");

    private final String code;
    private final String desc;

    ProjectTypeEnums(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
