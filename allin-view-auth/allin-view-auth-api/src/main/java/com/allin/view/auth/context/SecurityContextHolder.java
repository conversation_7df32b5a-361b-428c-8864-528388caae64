package com.allin.view.auth.context;

import cn.hutool.core.util.StrUtil;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.allin.view.auth.constant.SecurityContextConstants;
import com.allin.view.auth.pojo.dto.JwtToken;
import com.allin.view.auth.pojo.vo.ApiKeyVo;
import com.allin.view.base.exception.service.ValidationFailureException;
import org.springframework.lang.NonNull;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 获取当前线程变量中的 用户id、Token等信息
 * 注意: 必须在请求头中携带token，同时在AuthInterceptor拦截器设置值。 否则这里无法获取
 *
 * <AUTHOR>
 * @see SecurityContextConstants
 */
public class SecurityContextHolder {

    private static final TransmittableThreadLocal<Map<String, Object>> THREAD_LOCAL = new TransmittableThreadLocal<>();

    private SecurityContextHolder() {

    }

    /**
     * 获取当前线程变量中的Map
     *
     * <AUTHOR>
     * @since 2025/4/24
     */
    public static Map<String, Object> getLocalMap() {
        Map<String, Object> map = THREAD_LOCAL.get();
        if (map == null) {
            map = new ConcurrentHashMap<>();
            THREAD_LOCAL.set(map);
        }
        return map;
    }

    /**
     * 添加上下文传递的键值对, 如果值为空则不会添加
     *
     * @param key   键
     * @param value 值
     */
    public static void set(String key, Object value) {
        if (value == null) {
            return;
        }
        Map<String, Object> map = getLocalMap();
        map.put(key, value);
    }

    /**
     * 获取指定key的值
     *
     * @param key 键
     */
    @Deprecated(forRemoval = true)
    public static String getString(String key) {
        Map<String, Object> map = getLocalMap();
        return String.valueOf(map.getOrDefault(key, StrUtil.EMPTY));
    }

    /**
     * 获取指定Key的值，返回值为Object/T
     *
     * @param key 键
     */
    @SuppressWarnings("unchecked")
    public static <T> T getObject(String key) {
        Map<String, Object> map = getLocalMap();
        return (T) map.getOrDefault(key, null);
    }

    /**
     * 判断是否存在该key的值
     */
    public static boolean exist(String key) {
        Map<String, Object> map = getLocalMap();
        return map.containsKey(key);
    }

    /**
     * 清空
     */
    public static void remove() {
        THREAD_LOCAL.remove();
    }

    /**
     * 获取用户唯一标识
     */
    public static String getUserId() {
        return getJwtToken().getUser_id();
    }

    /**
     * 获取当前登录用户
     */
    @NonNull
    public static JwtToken getJwtToken() {
        final JwtToken jwtToken = getObject(SecurityContextConstants.USER_INFO);
        if (jwtToken == null) {
            throw new ValidationFailureException("当前用户未登录！");
        }
        return jwtToken;
    }

    /**
     * 获取当前项目id
     */
    public static String getProjectId() {
        final String projectId = getObject(SecurityContextConstants.PROJECT_ID);
        if (projectId == null) {
            throw new ValidationFailureException("获取项目id失败！");
        }
        return projectId;
    }

    /**
     * 设置当前项目id
     */
    public static void setProjectId(String projectId) {
        set(SecurityContextConstants.PROJECT_ID, projectId);
    }

    /**
     * 尝试获取当前登录用户
     */
    public static Optional<JwtToken> tryGetLoginUser() {
        final JwtToken jwtToken = getObject(SecurityContextConstants.USER_INFO);
        if (jwtToken == null) {
            return Optional.empty();
        }
        return Optional.of(jwtToken);
    }

    /**
     * 尝试获取当前项目id
     */
    public static Optional<String> tryGetProjectId() {
        return Optional.ofNullable(getObject(SecurityContextConstants.PROJECT_ID));
    }

    /**
     * 尝试获取当前登录用户
     */
    public static Optional<ApiKeyVo> tryGetThirdPartyInfo() {
        final ApiKeyVo tokenVo = getObject(SecurityContextConstants.API_KEY_INFO);
        if (Objects.isNull(tokenVo) || StrUtil.isBlankIfStr(tokenVo)) {
            return Optional.empty();
        }
        return Optional.of(tokenVo);
    }
}
