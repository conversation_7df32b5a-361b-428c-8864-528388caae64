package com.allin.view.auth.enums;

import com.allin.view.base.enums.base.IEnums;

/**
 * 部门类型（1：机场集团 2：分公司 3：机场4：部门）
 */
public enum DeptTypeEnums implements IEnums {
    AIRPORT_GROUP(1, "机场集团"),
    BRANCH_COMPANY(2, "分公司"),
    AIRPORT(3, "机场"),
    DEPARTMENT(4, "部门");

    private final Integer code;
    private final String desc;

    DeptTypeEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
