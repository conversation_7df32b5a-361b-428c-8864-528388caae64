package com.allin.view.auth.api;

import com.allin.view.auth.pojo.vo.ProjectPermissionVo;
import com.allin.view.base.domain.Result;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.service.annotation.GetExchange;
import org.springframework.web.service.annotation.HttpExchange;

/**
 * 项目权限 api
 * <AUTHOR>
 * @since 2025/2/28
 */
@HttpExchange("/sys_role")
public interface ProjectPermissionApi {

    /**
     * 查询当前用户拥有的项目的所有权限
     * <AUTHOR>
     * @since 2025/2/28
     */
    @GetExchange("/project_user_permission")
    Result<ProjectPermissionVo> getProjectPermission(@RequestParam("userId") String userId, @RequestParam("projectId") String projectId);

}
