package com.allin.view.auth.interceptor;

import cn.hutool.core.util.StrUtil;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.auth.utils.TokenUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 项目id拦截器
 *
 * <AUTHOR>
 */
public class ProjectIdInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        final String header = TokenUtils.getProjectId(request);
        if (StrUtil.isBlank(header)) {
            return true;
        }
        // 设置项目ID到上下文
        SecurityContextHolder.setProjectId(header);
        return true;
    }
}
