package com.allin.view.auth.pojo.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * Redis 缓存的用户在线信息
 * <AUTHOR>
 * @date 2024/2/28
 */
@Data
public class OnlineUser {

    /**
     * 验证令牌
     */
    private String token;

    /**
     * 登录时间
     */
    private LocalDateTime loginTime;

    /**
     * 过期时间
     */
    private LocalDateTime expireTime;

    /**
     * 登录ip
     */
    private String loginIp;

    /**
     * false 说明是PC端令牌
     */
    private boolean isApp;

    /**
     * 密码是否已过期
     */
    private boolean isPasswordExpired;
}
