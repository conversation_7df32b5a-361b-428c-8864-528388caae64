package com.allin.view.auth.api;

import com.allin.view.auth.pojo.dto.ApiKeyCheckDto;
import com.allin.view.auth.pojo.vo.ApiKeyVo;
import com.allin.view.base.domain.Result;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.GetExchange;
import org.springframework.web.service.annotation.HttpExchange;

/**
 * api key API
 *
 * <AUTHOR>
 * @since 2025/7/29
 */
@HttpExchange("/api_key")
public interface ApiKeyApi {

    /**
     * 验证Apikey及其权限
     */
    @GetExchange("/check")
    Result<ApiKeyVo> check(@RequestBody @Validated ApiKeyCheckDto checkDto);
}