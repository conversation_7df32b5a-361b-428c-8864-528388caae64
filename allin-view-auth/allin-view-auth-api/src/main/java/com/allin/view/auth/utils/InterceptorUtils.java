package com.allin.view.auth.utils;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.net.NetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpStatus;
import com.alibaba.fastjson2.JSON;
import com.allin.view.auth.constant.TokenConstants;
import com.allin.view.auth.interceptor.annotation.WhiteListApi;
import com.allin.view.base.domain.Result;
import com.allin.view.base.utils.ServletUtils;
import com.allin.view.base.utils.ip.IpV4Utils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.method.HandlerMethod;

import java.io.IOException;
import java.util.Collection;
import java.util.Set;

/**
 * 拦截器工具类
 *
 * <AUTHOR>
 * @since 2025/3/14
 */
@Slf4j
public class InterceptorUtils {

    private InterceptorUtils() {
    }

    /**
     * 查找指定字符串是否匹配指定字符串列表中的任意一个字符串
     *
     * @param str  指定字符串
     * @param strs 需要检查的字符串数组
     * @return 是否匹配
     */
    public static boolean matches(String str, Collection<String> strs) {
        if (StrUtil.isEmpty(str) || CollUtil.isEmpty(strs)) {
            return false;
        }
        for (String pattern : strs) {
            // 判断url是否与规则配置
            AntPathMatcher matcher = new AntPathMatcher();
            if (matcher.match(pattern, str)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 响应错误信息到客户端
     *
     * @param response HTTP响应对象
     * @param msg      错误信息
     * @throws IOException 写入响应时可能发生IO异常
     */
    public static boolean writerResponse(HttpServletResponse response, String msg) throws IOException {
        return writerResponse(HttpStatus.HTTP_UNAUTHORIZED, response, msg);
    }

    /**
     * 响应错误信息到客户端
     *
     * @param response HTTP响应对象
     * @param msg      错误信息
     * @throws IOException 写入响应时可能发生IO异常
     */
    public static boolean writerResponse(Integer code, HttpServletResponse response, String msg) throws IOException {
        response.setStatus(code);
        response.setContentType("application/json");
        response.setCharacterEncoding("utf-8");
        response.getWriter().println(JSON.toJSONString(Result.fail(code, msg)));
        return false;
    }

    /**
     * 判断是否为OPTIONS预检请求
     *
     * @param request HTTP请求对象
     * @return 如果是OPTIONS请求返回true，否则返回false
     */
    public static boolean isOptionsRequest(HttpServletRequest request) {
        return HttpMethod.OPTIONS.name().equalsIgnoreCase(request.getMethod());
    }

    /**
     * 检查请求IP是否在白名单中
     *
     * @param response HTTP响应对象
     * @param ipWhites 白名单列表
     * @return 如果IP在白名单中或满足白名单规则返回true，否则返回false
     * @throws IOException 写入响应时可能发生IO异常
     */
    public static boolean isIpInWhitelist(HttpServletResponse response,
                                          Set<String> ipWhites) throws IOException {
        // 如果白名单为空或包含0.0.0.0，则跳过IP校验
        if (CollUtil.isEmpty(ipWhites) || ipWhites.contains("0.0.0.0")) {
            return true;
        }

        String requestIp = IpV4Utils.getIpAddr(ServletUtils.getRequest());
        // 本地请求直接放行
        if (StrUtil.isBlank(requestIp) || NetUtil.LOCAL_IP.equalsIgnoreCase(requestIp)) {
            return true;
        }
        if (!IpV4Utils.isIPAddressMatch(requestIp, ipWhites)) {
            return writerResponse(response, StrUtil.format("{} 非法 ip 访问", requestIp));
        }
        return true;
    }

    /**
     * 检查请求是否为白名单请求
     * 白名单请求包括：
     * 1. 类上标注了@WhiteListApi注解
     * 2. 方法上标注了@WhiteListApi注解
     * 3. 请求路径匹配白名单配置
     *
     * @param request       HTTP请求对象
     * @param requestMethod 处理方法对象
     * @return 如果是白名单请求返回true，否则返回false
     */
    public static boolean isWhiteListRequest(HttpServletRequest request,
                                             HandlerMethod requestMethod,
                                             Set<String> apiWhites) {
        // 检查类或方法是否标注了白名单注解
        return requestMethod.getBeanType().isAnnotationPresent(WhiteListApi.class)
               || requestMethod.hasMethodAnnotation(WhiteListApi.class)
               || matches(request.getRequestURI(), apiWhites);
    }

    /**
     * 是否为内部服务调用
     */
    public static boolean isInternalService(HttpServletRequest request) {
        return request.getHeader(TokenConstants.serviceAuthHeader) != null;
    }
}
