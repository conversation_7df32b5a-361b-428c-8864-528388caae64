package com.allin.view.auth;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.allin.view.auth.api.ApiKeyApi;
import com.allin.view.auth.api.ServiceApi;
import com.allin.view.auth.api.TokenApi;
import com.allin.view.auth.api.UserPermissionApi;
import com.allin.view.auth.api.config.AuthApiProxyClientConfig;
import com.allin.view.auth.config.properties.AuthApiProperties;
import com.allin.view.auth.heartbeat.ServiceHeartbeatTask;
import com.allin.view.auth.interceptor.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

/**
 * 依赖auth服务认证的自动配置，在auth服务中该自动配置不会生效
 *
 * <AUTHOR>
 */
@Slf4j
@EnableScheduling
@AutoConfiguration
@ConditionalOnMissingBean(name = "authServiceConfig")
@EnableConfigurationProperties({AuthApiProperties.class})
@Import({AuthApiProxyClientConfig.class}) // 导入其他配置类
public class AutApiAutoConfiguration {

    private final AuthApiProperties authApiProperties;

    private final ApiKeyApi apiKeyApi;

    private final TokenApi tokenApi;

    private final UserPermissionApi userPermissionApi;

    public AutApiAutoConfiguration(AuthApiProperties authApiProperties,
                                   ApiKeyApi apiKeyApi,
                                   TokenApi tokenApi,
                                   UserPermissionApi userPermissionApi) {
        this.authApiProperties = authApiProperties;
        this.apiKeyApi = apiKeyApi;
        this.tokenApi = tokenApi;
        this.userPermissionApi = userPermissionApi;
    }

    @Bean
    public UserTokenInterceptor defaultUserAuthInterceptor(List<ExtendUserTokenInterceptor> extendAuthInterceptors) {
        log.info("认证属性加载成功...,{}", JSON.toJSON(authApiProperties));
        if (CollUtil.isNotEmpty(extendAuthInterceptors)) {
            for (ExtendUserTokenInterceptor extendUserTokenInterceptor : extendAuthInterceptors) {
                log.info("扩展拦截器加载成功...,{}", extendUserTokenInterceptor.interceptorName());
            }
        }
        return new UserTokenInterceptor(tokenApi, authApiProperties, extendAuthInterceptors);
    }

    @Bean
    public PermissionInterceptor defaultPermissionInterceptor() {
        return new PermissionInterceptor(userPermissionApi, authApiProperties);
    }

    @Bean
    public ApiKeyInterceptor defaultServiceTokenAuthInterceptor() {
        return new ApiKeyInterceptor(apiKeyApi);
    }

    @Bean
    public IpWhiteInterceptor ipWhiteInterceptor() {
        return new IpWhiteInterceptor(authApiProperties);
    }

    @Bean
    public ProjectIdInterceptor projectIdInterceptor() {
        return new ProjectIdInterceptor();
    }

    @Bean
    public WebMvcConfigurer customWebMvcConfig(UserTokenInterceptor authInterceptor,
                                               PermissionInterceptor permissionInterceptor,
                                               ApiKeyInterceptor apiKeyInterceptor,
                                               IpWhiteInterceptor ipWhiteInterceptor,
                                               ProjectIdInterceptor projectIdInterceptor) {
        return new WebMvcConfigurer() {
            @Override
            public void addInterceptors(InterceptorRegistry registry) {
                registry.addInterceptor(projectIdInterceptor).addPathPatterns("/**");
                log.info("项目id拦截器加载成功...");
                registry.addInterceptor(apiKeyInterceptor).addPathPatterns("/**");
                log.info("ApiKey令牌认证拦截器加载成功...");
                registry.addInterceptor(ipWhiteInterceptor).addPathPatterns("/**");
                log.info("白名单IP拦截器加载成功...");
                registry.addInterceptor(authInterceptor).addPathPatterns("/**");
                log.info("用户Token认证拦截器加载成功...");
                registry.addInterceptor(permissionInterceptor).addPathPatterns("/**");
                log.info("用户权限认证拦截器加载成功...");
            }
        };
    }

    /**
     * 心跳连接
     */
    @Bean
    public ServiceHeartbeatTask serviceRegisterTask(ServiceApi serviceApi, AuthApiProperties authApiProperties) {
        return new ServiceHeartbeatTask(serviceApi, authApiProperties);
    }
}
