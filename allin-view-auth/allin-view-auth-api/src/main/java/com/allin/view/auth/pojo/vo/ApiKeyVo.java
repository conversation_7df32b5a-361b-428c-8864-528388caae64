package com.allin.view.auth.pojo.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 第三方服务令牌
 *
 * <AUTHOR>
 * @date 2024/5/30
 */
@Data
public class ApiKeyVo {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 服务名称
     */
    private String serviceName;

    /**
     * 令牌
     */
    private String token;

    /**
     * 权限字符串, 多个之间逗号隔开
     */
    private String permissions;

    /**
     * 过期时间
     */
    private LocalDateTime expireTime;

    /**
     * 是否启用 (1-启用, 0-禁用)
     */
    private Integer enabled;
}