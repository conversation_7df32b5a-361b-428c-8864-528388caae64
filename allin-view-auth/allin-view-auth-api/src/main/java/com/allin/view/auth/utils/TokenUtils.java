package com.allin.view.auth.utils;


import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.JakartaServletUtil;
import com.allin.view.auth.constant.RequestHeaderConstants;
import com.allin.view.auth.constant.TokenConstants;
import jakarta.annotation.Nullable;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * token验证工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class TokenUtils {

    /**
     * 裁剪token前缀
     */
    private static String replaceTokenPrefix(String token) {
        if (StrUtil.isBlank(token)) {
            return null;
        }
        // 如果前端设置了令牌前缀，则裁剪掉前缀
        if (token.startsWith(TokenConstants.PREFIX)) {
            token = token.replaceFirst(TokenConstants.PREFIX, "");
        }
        return token;
    }

    @Nullable
    private static String getRequestParam(HttpServletRequest request, String paramKey) {
        // 从header获取token标识
        String param = request.getHeader(paramKey);

        // 从请求参数获取
        if (StrUtil.isBlank(param)) {
            param = request.getParameter(paramKey);
        }

        // 从 cookie 获取
        if (StrUtil.isBlank(param)) {
            final Cookie cookie = JakartaServletUtil.getCookie(request, paramKey);
            if (cookie != null) {
                param = cookie.getValue();
            }
        }

        if (StrUtil.isBlank(param)) {
            return null;
        }

        return param;
    }

    /**
     * 根据request获取用户token
     */
    public static String getToken(HttpServletRequest request) {
        String token = getRequestParam(request, RequestHeaderConstants.TOKEN);
        if (StrUtil.isBlank(token)) {
            return null;
        }
        return replaceTokenPrefix(token);
    }

    /**
     * 根据request获取项目id
     */
    public static String getProjectId(HttpServletRequest request) {
        String token = getRequestParam(request, RequestHeaderConstants.PROJECT_ID);
        if (StrUtil.isBlank(token)) {
            return null;
        }
        return replaceTokenPrefix(token);
    }

    /**
     * 根据request获取第三方令牌
     */
    public static String getApiKey(HttpServletRequest request) {
        String token = getRequestParam(request, RequestHeaderConstants.APK_KEY);
        if (StrUtil.isBlank(token)) {
            return null;
        }
        return replaceTokenPrefix(token);
    }
}
