package com.allin.view.auth.api.config;

import cn.hutool.core.net.NetUtil;
import cn.hutool.core.util.StrUtil;
import com.allin.view.auth.api.*;
import com.allin.view.auth.config.properties.AuthApiProperties;
import com.allin.view.auth.constant.TokenConstants;
import com.allin.view.auth.utils.TokenUtils;
import com.allin.view.base.utils.ServletUtils;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.HttpHeaders;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestClient;
import org.springframework.web.client.support.RestClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;

import java.nio.charset.StandardCharsets;

/**
 * api客户端配置
 *
 * <AUTHOR>
 * @date 2024/2/28
 */
@Slf4j
@Configuration
public class AuthApiProxyClientConfig {

    private final AuthApiProperties authApiProperties;

    private final MappingJackson2HttpMessageConverter jackson2HttpMessageConverter;

    @Value("${server.port}")
    private String port;

    public AuthApiProxyClientConfig(AuthApiProperties authApiProperties,
                                    MappingJackson2HttpMessageConverter jackson2HttpMessageConverter) {
        this.authApiProperties = authApiProperties;
        this.jackson2HttpMessageConverter = jackson2HttpMessageConverter;
        log.info("api 客户端配置加载成功...");
    }

    /**
     * auth 服务专用客户端
     */
    @Bean("authServiceClient")
    public RestClient authServiceClient(ClientHttpRequestFactory factory) {
        String url = authApiProperties.getUrl();
        if (StrUtil.isBlank(url)) {
            url = "http://127.0.0.1:" + port;
        }
        return RestClient.builder()
                .baseUrl(url)
                .requestFactory(factory)
                .requestInitializer(request -> {
                    HttpServletRequest httpServletRequest = null;
                    try {
                        httpServletRequest = ServletUtils.getRequest();
                    } catch (Exception ignore) {
                        // 忽略异常，服务内部主动发起的调用会出现拿不到 request 的情况
                    } finally {
                        final HttpHeaders headers = request.getHeaders();
                        if (httpServletRequest != null) {
                            headers.add(TokenConstants.TOKEN, TokenUtils.getToken(httpServletRequest));
                        }
                        // 配置客户端IP
                        final String localhostStr = NetUtil.getLocalhostStr();
                        if (localhostStr != null) {
                            headers.add("X-Forwarded-For", NetUtil.getLocalhostStr());
                        }
                        headers.add(TokenConstants.serviceAuthHeader, TokenConstants.serviceAuthHeaderON);
                        headers.add(HttpHeaders.ACCEPT, "application/json");
                    }
                })
                .messageConverters(converters -> {
                    converters.removeIf(item -> item.getClass().isAssignableFrom(MappingJackson2HttpMessageConverter.class));
                    converters.add(jackson2HttpMessageConverter);
                })
                // 添加拦截器以打印请求和响应
                .requestInterceptors(interceptors -> interceptors.add((request, body, execution) -> {
                    log.debug("Request URI: {}, Method: {}", request.getURI(), request.getMethod());
                    log.debug("Request Headers: {}", request.getHeaders());
                    log.debug("Request Body: {}", new String(body, StandardCharsets.UTF_8));
                    ClientHttpResponse response = execution.execute(request, body);
                    return response;
                }))
                .build();
    }

    @Bean
    public TokenApi tokenApi(@Qualifier("authServiceClient") RestClient authServiceClient) {
        HttpServiceProxyFactory httpServiceProxyFactory = HttpServiceProxyFactory
                .builderFor(RestClientAdapter.create(authServiceClient))
                .build();
        return httpServiceProxyFactory.createClient(TokenApi.class);
    }

    @Bean
    public ApiKeyApi apiKeyApi(@Qualifier("authServiceClient") RestClient authServiceClient) {
        HttpServiceProxyFactory httpServiceProxyFactory = HttpServiceProxyFactory
                .builderFor(RestClientAdapter.create(authServiceClient))
                .build();
        return httpServiceProxyFactory.createClient(ApiKeyApi.class);
    }

    @Primary
    @Bean
    public UserInfoQueryApi userInfoQueryApi(@Qualifier("authServiceClient") RestClient authServiceClient) {
        HttpServiceProxyFactory httpServiceProxyFactory = HttpServiceProxyFactory
                .builderFor(RestClientAdapter.create(authServiceClient))
                .build();
        return httpServiceProxyFactory.createClient(UserInfoQueryApi.class);
    }

    @Bean
    public SysDepartApi sysDepartApi(@Qualifier("authServiceClient") RestClient authServiceClient) {
        HttpServiceProxyFactory httpServiceProxyFactory = HttpServiceProxyFactory
                .builderFor(RestClientAdapter.create(authServiceClient))
                .build();
        return httpServiceProxyFactory.createClient(SysDepartApi.class);
    }

    @Bean
    public ServiceApi serviceApi(@Qualifier("authServiceClient") RestClient authServiceClient) {
        HttpServiceProxyFactory httpServiceProxyFactory = HttpServiceProxyFactory
                .builderFor(RestClientAdapter.create(authServiceClient))
                .build();
        return httpServiceProxyFactory.createClient(ServiceApi.class);
    }

    @Bean
    public UserPermissionApi userPermissionApi(@Qualifier("authServiceClient") RestClient authServiceClient) {
        HttpServiceProxyFactory httpServiceProxyFactory = HttpServiceProxyFactory
                .builderFor(RestClientAdapter.create(authServiceClient))
                .build();
        return httpServiceProxyFactory.createClient(UserPermissionApi.class);
    }
}
