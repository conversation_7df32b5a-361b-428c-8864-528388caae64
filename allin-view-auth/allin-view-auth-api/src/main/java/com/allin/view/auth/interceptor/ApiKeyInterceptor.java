package com.allin.view.auth.interceptor;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.allin.view.auth.api.ApiKeyApi;
import com.allin.view.auth.constant.SecurityContextConstants;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.auth.interceptor.annotation.ApiKeyVerify;
import com.allin.view.auth.pojo.dto.ApiKeyCheckDto;
import com.allin.view.auth.pojo.vo.ApiKeyVo;
import com.allin.view.auth.utils.InterceptorUtils;
import com.allin.view.auth.utils.TokenUtils;
import com.allin.view.base.domain.Result;
import com.allin.view.base.i18n.I18nUtil;
import com.allin.view.base.utils.api.ApiUtils;
import jakarta.annotation.Nullable;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * ApiKey请求认证拦截器，会校验token并将ApiKey数据封装到线程变量中方便获取
 *
 * <AUTHOR>
 */
public class ApiKeyInterceptor implements HandlerInterceptor {

    private final ApiKeyApi apiKeyApi;

    /**
     * 权限缓存
     */
    private final TimedCache<String, ApiKeyVo> apiKeyCacheMap = CacheUtil.newTimedCache(60000, 30000);

    public ApiKeyInterceptor(ApiKeyApi apiKeyApi) {
        this.apiKeyApi = apiKeyApi;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
                                Exception ex) {
        SecurityContextHolder.remove();
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws IOException {
        if (InterceptorUtils.isOptionsRequest(request) || InterceptorUtils.isInternalService(request)) {
            return true;
        }

        // 如果不是映射到方法，直接通过
        if (!(handler instanceof HandlerMethod requestMethod)) {
            return true;
        }

        // apiKey 和 权限校验
        final String token = TokenUtils.getApiKey(request);
        if (StrUtil.isBlank(token)) {
            return true;
        }

        // 检查类或方法是否标注了注解
        final ApiKeyVerify apiKeyVerify = isRequireValidate(requestMethod);
        if (apiKeyVerify == null) {
            return InterceptorUtils.writerResponse(HttpServletResponse.SC_FORBIDDEN, response, I18nUtil.getMessage("auth.not.permission"));
        }

        // 获取注解中的权限字符串
        List<String> permissions = Arrays.asList(apiKeyVerify.value());

        // 从缓存中获取用户权限
        ApiKeyVo apiKeyVo = apiKeyCacheMap.get(token, false);

        if (apiKeyVo == null) {
            final Result<ApiKeyVo> check = ApiUtils.getResult(() -> apiKeyApi.check(new ApiKeyCheckDto(token)));
            if (check.isOk()) {
                apiKeyVo = check.getData();
                apiKeyCacheMap.put(token, apiKeyVo);
            } else {
                // 没有权限时可以返回403或者其他处理
                return InterceptorUtils.writerResponse(HttpServletResponse.SC_FORBIDDEN, response, check.getMessage());
            }
        }

        if (apiKeyVo == null) {
            // 没有权限时可以返回403或者其他处理
            return InterceptorUtils.writerResponse(HttpServletResponse.SC_FORBIDDEN, response, I18nUtil.getMessage("auth.not.permission"));
        }

        // 用 Set 进行权限判断提高效率
        Set<String> permissionSet = new HashSet<>(StrUtil.split(apiKeyVo.getPermissions(), ","));
        // 如果为空或者包含*或者存在交集旧认为拥有权限
        if (CollUtil.isEmpty(permissionSet) || permissionSet.contains("*") || CollUtil.containsAny(permissionSet, permissions)) {
            // 将第三方用户信息放入线程上下文中
            SecurityContextHolder.set(SecurityContextConstants.API_KEY_INFO, apiKeyVo);
            return true;
        }

        // 没有权限时可以返回403或者其他处理
        return InterceptorUtils.writerResponse(HttpServletResponse.SC_FORBIDDEN, response, I18nUtil.getMessage("auth.not.permission"));
    }

    /**
     * 检查请求是否需要第三方令牌校验，类或方法上标注了注解
     */
    @Nullable
    private ApiKeyVerify isRequireValidate(HandlerMethod requestMethod) {
        ApiKeyVerify apiKeyVerify = requestMethod.getMethod().getAnnotation(ApiKeyVerify.class);

        if (apiKeyVerify == null && requestMethod.getBeanType().isAnnotationPresent(ApiKeyVerify.class)) {
            apiKeyVerify = requestMethod.getBeanType().getAnnotation(ApiKeyVerify.class);
        }

        return apiKeyVerify;
    }
}
