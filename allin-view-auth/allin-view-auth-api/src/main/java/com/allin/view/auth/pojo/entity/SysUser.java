package com.allin.view.auth.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 系统用户信息表
 */
@Data
@TableName(value = "sys_user")
public class SysUser {
    /**
     * 雪花id, 用户唯一标识
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 账号(用户名)
     */
    @TableField(value = "user_name")
    private String userName;

    /**
     * 姓名
     */
    @TableField(value = "full_name")
    private String fullName;

    /**
     * 密码
     */
    @TableField(value = "password")
    private String password;

    /**
     * 头像
     */
    @TableField(value = "head_img")
    private String headImg;

    /**
     * 性别(男, 女)
     */
    @TableField(value = "sex")
    private String sex;

    /**
     * 手机号码
     */
    @TableField(value = "phone_numb")
    private String phoneNumb;

    /**
     * 账号状态，0禁用，1启用
     */
    @TableField(value = "status")
    private Boolean status;

    /**
     * 是否超级管理员，0不是，1是
     */
    @TableField(value = "is_super_admin")
    private Boolean isSuperAdmin;

    /**
     * 公司id/租户id
     */
    @TableField(value = "tenant_id")
    private String tenantId;

    /**
     * 部门id
     */
    @TableField(value = "dept_id")
    private String deptId;

    /**
     * 备注
     */
    @TableField(value = "remarks")
    private String remarks;

    /**
     * 创建人
     */
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT)
    private LocalDateTime updatedTime;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableField(value = "deleted", fill = FieldFill.INSERT)
    private Integer deleted;

    /**
     * 排序
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 密码最后修改日期
     */
    @TableField(value = "password_last_updated")
    private LocalDateTime passwordLastUpdated;
}