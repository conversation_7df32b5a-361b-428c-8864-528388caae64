package com.allin.view.auth.serializer.annotation;

import com.allin.view.auth.serializer.DeptIdAsNullSerializer;
import com.allin.view.auth.serializer.DeptIdSerializer;
import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/**
 * 部门id序列化转换, 支持String和List
 * <pre>
 * {@code
 *    @DeptIdToName
 *    private Long deptId;
 *
 *    // 单个部门id
 *    @DeptIdToName
 *    private String deptId;
 *
 *    @DeptIdToName
 *    private List<Long> deptId;
 *
 * }
 * </pre>
 *
 * <AUTHOR>
 * @date 2023/10/10
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@JacksonAnnotationsInside
@JsonSerialize(nullsUsing = DeptIdAsNullSerializer.class, using = DeptIdSerializer.class)
public @interface DeptIdToName {

    /**
     * 如果配置了key就会将转换后的值放入新的key中，被标记的字段的值依然不变
     * @see com.allin.view.config.serialize.annotation.ApiFoxNoIgnore
     */
    String key() default "";

    /**
     * 设置为true的情况就转换格式为 xx公司/xx部门
     */
    boolean isAllPath() default false;

    /**
     * 是否返回List类型
     */
    boolean isList() default false;
}
