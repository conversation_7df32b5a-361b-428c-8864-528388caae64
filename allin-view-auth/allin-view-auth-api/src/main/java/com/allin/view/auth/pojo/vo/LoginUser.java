package com.allin.view.auth.pojo.vo;

import com.allin.view.auth.pojo.dto.OnlineUser;
import com.allin.view.auth.pojo.entity.SysUser;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.BeanUtils;

/**
 * 登录时返回的信息
 * <AUTHOR>
 * @date 2024/2/28
 */
@Data
@EqualsAndHashCode(callSuper=false)
public class LoginUser extends OnlineUser {

    /**
     * 用户详细信息
     */
    private SysUserVo userInfo;

    public LoginUser() {

    }

    public LoginUser(OnlineUser onlineUser, SysUser sysUser) {
        setToken(onlineUser.getToken());
        setLoginTime(onlineUser.getLoginTime());
        setExpireTime(onlineUser.getExpireTime());
        setLoginIp(onlineUser.getLoginIp());
        final SysUserVo userVo = new SysUserVo();
        BeanUtils.copyProperties(sysUser, userVo);
        this.userInfo = userVo;
    }

    @JsonIgnore
    public String getUserName(){
        return userInfo.getUserName();
    }

    @JsonIgnore
    public String getUserId(){
        return userInfo.getId();
    }
}
