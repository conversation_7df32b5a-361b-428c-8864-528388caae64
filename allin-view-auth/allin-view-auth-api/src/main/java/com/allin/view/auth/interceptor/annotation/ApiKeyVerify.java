package com.allin.view.auth.interceptor.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/**
 * 校验ApiKey的注解
 *
 * <AUTHOR>
 * @since 2025/7/29
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ApiKeyVerify {

    /**
     * 权限字符串
     */
    String[] value() default {};
}
