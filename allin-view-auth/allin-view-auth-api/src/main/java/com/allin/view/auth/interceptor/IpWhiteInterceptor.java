package com.allin.view.auth.interceptor;

import com.allin.view.auth.config.properties.AuthApiProperties;
import com.allin.view.auth.constant.SecurityContextConstants;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.auth.utils.InterceptorUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import java.io.IOException;

/**
 * IP白名单拦截器
 *
 * <AUTHOR>
 */
public class IpWhiteInterceptor implements HandlerInterceptor {

    private final AuthApiProperties authApiProperties;

    public IpWhiteInterceptor(AuthApiProperties authApiProperties) {
        this.authApiProperties = authApiProperties;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws IOException {
        // 如果不是映射到方法，直接通过
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        // OPTIONS请求直接放行，服务内部请求直接跳过后续校验
        if (InterceptorUtils.isOptionsRequest(request) || InterceptorUtils.isInternalService(request)) {
            return true;
        }

        // 第三方令牌校验已通过不执行白名单校验
        if (SecurityContextHolder.exist(SecurityContextConstants.API_KEY_INFO)) {
            return true;
        }

        // IP白名单校验
        return InterceptorUtils.isIpInWhitelist(response, authApiProperties.getIpWhites());
    }
}
