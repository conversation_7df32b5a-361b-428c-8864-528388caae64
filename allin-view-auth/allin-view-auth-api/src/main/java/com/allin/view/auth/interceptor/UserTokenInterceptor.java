package com.allin.view.auth.interceptor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.StrUtil;
import cn.hutool.jwt.JWTValidator;
import com.allin.view.auth.api.TokenApi;
import com.allin.view.auth.config.properties.AuthApiProperties;
import com.allin.view.auth.constant.RequestHeaderConstants;
import com.allin.view.auth.constant.SecurityContextConstants;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.auth.pojo.dto.JwtToken;
import com.allin.view.auth.pojo.dto.TokenDto;
import com.allin.view.auth.utils.InterceptorUtils;
import com.allin.view.auth.utils.TokenUtils;
import com.allin.view.base.domain.Result;
import com.allin.view.base.utils.api.ApiUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import java.io.IOException;
import java.util.List;

/**
 * 用户token认证拦截器，会校验token并将用户信息数据封装到线程变量中方便获取
 *
 * <AUTHOR>
 */
@Slf4j
public class UserTokenInterceptor implements HandlerInterceptor {

    private final TokenApi tokenApi;

    private final AuthApiProperties authApiProperties;

    private final List<ExtendUserTokenInterceptor> extendAuthInterceptors;

    public UserTokenInterceptor(TokenApi tokenApi,
                                AuthApiProperties authApiProperties,
                                List<ExtendUserTokenInterceptor> extendAuthInterceptors) {
        this.tokenApi = tokenApi;
        this.authApiProperties = authApiProperties;
        this.extendAuthInterceptors = extendAuthInterceptors;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
                                Exception ex) {
        SecurityContextHolder.remove();
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws IOException {
        // OPTIONS 请求直接放行
        if (InterceptorUtils.isOptionsRequest(request) || InterceptorUtils.isInternalService(request)) {
            return true;
        }

        // 如果不是映射到方法，直接通过
        if (!(handler instanceof HandlerMethod requestMethod)) {
            return true;
        }

        // 第三方令牌校验已通过不执行校验
        if (SecurityContextHolder.exist(SecurityContextConstants.API_KEY_INFO)) {
            return true;
        }

        // 白名单请求校验
        if (InterceptorUtils.isWhiteListRequest(request, requestMethod, authApiProperties.getApiWhites())) {
            return true;
        }

        // 扩展认证处理
        if (CollUtil.isNotEmpty(extendAuthInterceptors)) {
            for (ExtendUserTokenInterceptor extendAuthInterceptor : extendAuthInterceptors) {
                if (extendAuthInterceptor.preHandle(request, response, handler)) {
                    return true;
                }
            }
        }

        // Token校验
        return validateToken(request, response);
    }

    /**
     * 验证Token的有效性
     * 验证通过后会将用户信息存入SecurityContextHolder
     *
     * @param request  HTTP请求对象
     * @param response HTTP响应对象
     * @return 如果Token验证通过返回true，否则返回false
     * @throws IOException 写入响应时可能发生IO异常
     */
    private boolean validateToken(HttpServletRequest request, HttpServletResponse response) throws IOException {
        final String token = TokenUtils.getToken(request);
        if (StrUtil.isBlank(token)) {
            return InterceptorUtils.writerResponse(response, "请先登录！");
        }

        if (!JwtToken.verify(token)) {
            return InterceptorUtils.writerResponse(response, "无效凭证！");
        }

        try {
            JWTValidator.of(token).validateDate();
        } catch (ValidateException e) {
            log.info("token 已过期, 尝试刷新 token", e);
            final Result<String> newToken = ApiUtils.getResult(() -> tokenApi.refreshToken(new TokenDto(token)));
            if (newToken.isOk()) {
                response.setHeader(RequestHeaderConstants.X_NEW_TOKEN, newToken.getData());
            } else {
                return InterceptorUtils.writerResponse(response, newToken.getMessage());
            }
        }

        // 将 Token 解析放入线程上下文中
        SecurityContextHolder.set(SecurityContextConstants.USER_INFO, JwtToken.parse(token));
        return true;
    }
}
