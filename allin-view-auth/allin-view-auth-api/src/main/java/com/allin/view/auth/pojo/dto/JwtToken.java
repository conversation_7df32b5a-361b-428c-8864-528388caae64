package com.allin.view.auth.pojo.dto;

import cn.hutool.jwt.JWTUtil;
import com.allin.view.auth.constant.TokenConstants;
import lombok.Data;

import java.nio.charset.StandardCharsets;

/**
 * JWT 结构的 Token
 *
 * <AUTHOR>
 * @since 2025/7/21
 */
@Data
public class JwtToken {

    /**
     * 签发人
     */
    private String iss = "allin-view-auth-4.x";

    /**
     * 主题
     */
    private String sub;

    /**
     * 受众
     */
    private String aud = "*";

    /**
     * 过期时间 = 签发时间 + 有效时长
     */
    private Long exp;

    /**
     * 生效时间, 默认为当前时间
     */
    private Long nbf;

    /**
     * 签发时间, 默认为当前时间
     */
    private Long iat;

    /**
     * 刷新令牌id
     */
    private String refresh_id;

    /**
     * 令牌的唯一标识
     */
    private String jti;

    /**
     * 客户端类型
     */
    private String client_type;

    /**
     * 用户唯一标识
     */
    private String user_id;

    /**
     * 用户账号
     */
    private String user_name;

    /**
     * 姓名
     */
    private String full_name;

    /**
     * 头像
     */
    private String head_img;

    /**
     * 性别(男, 女)
     */
    private String sex;

    /**
     * 部门id
     */
    private String dept_id;

    /**
     * 是否为超级管理员
     */
    private Boolean is_super_admin;

    public JwtToken() {
    }

    public static JwtToken parse(String jwtToken) {
        return JWTUtil.parseToken(jwtToken).getPayloads().toBean(JwtToken.class);
    }

    public static boolean verify(String jwtToken) {
        return JWTUtil.verify(jwtToken, TokenConstants.SECRET.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 兼容驼峰写法
     */
    public String getUserId() {
        return user_id;
    }
}
