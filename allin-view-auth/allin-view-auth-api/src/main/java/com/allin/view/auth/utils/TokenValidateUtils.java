package com.allin.view.auth.utils;


import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.JakartaServletUtil;
import com.allin.view.auth.constant.SecurityContextConstants;
import com.allin.view.auth.constant.TokenConstants;
import com.allin.view.auth.pojo.dto.OnlineUser;
import com.allin.view.base.exception.service.ValidationFailureException;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtParser;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import jakarta.annotation.Nullable;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;

import javax.crypto.SecretKey;
import java.time.Duration;
import java.time.LocalDateTime;

/**
 * token验证工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class TokenValidateUtils {

    /**
     * 裁剪token前缀
     */
    private static String replaceTokenPrefix(String token) {
        if (StrUtil.isBlank(token)) {
            return null;
        }
        // 如果前端设置了令牌前缀，则裁剪掉前缀
        if (token.startsWith(TokenConstants.PREFIX)) {
            token = token.replaceFirst(TokenConstants.PREFIX, "");
        }
        return token;
    }

    @Nullable
    private static String getRequestParam(HttpServletRequest request, String paramKey) {
        // 从header获取token标识
        String param = request.getHeader(paramKey);

        // 从请求参数获取
        if (StrUtil.isBlank(param)) {
            param = request.getParameter(paramKey);
        }

        // 尝试从 cookie 获取
        final Cookie cookie = JakartaServletUtil.getCookie(request, paramKey);
        if (cookie != null) {
            param = cookie.getValue();
        }

        if (StrUtil.isBlank(param)) {
            return null;
        }

        return param;
    }

    /**
     * 根据request获取用户token
     */
    @Nullable
    public static String getToken(HttpServletRequest request) {
        String token = getRequestParam(request, TokenConstants.TOKEN);
        if (StrUtil.isBlank(token)) {
            return null;
        }
        return replaceTokenPrefix(token);
    }

    /**
     * 根据request获取项目id
     */
    @Nullable
    public static String getProjectId(HttpServletRequest request) {
        return getRequestParam(request, SecurityContextConstants.PROJECT_ID);
    }

    /**
     * 根据request获取第三方令牌
     */
    public static String getThirdPartyToken(HttpServletRequest request) {
        // 从header获取token标识
        String token = request.getHeader(TokenConstants.THIRD_PARTY_TOKEN);
        if (StrUtil.isEmpty(token)) {
            token = request.getParameter(TokenConstants.THIRD_PARTY_TOKEN);
        }
        return token;
    }

    /**
     * 从令牌中获取数据声明
     *
     * @param token 令牌
     * @return 数据声明
     */
    public static Claims parseJwt(String token) {
        // 1.对秘钥做BASE64编码
        String base64 = Base64.encode(TokenConstants.SECRET.getBytes());
        // 2.生成秘钥对象,会根据base64长度自动选择相应的 HMAC 算法
        SecretKey secretKey = Keys.hmacShaKeyFor(base64.getBytes());
        // 生成JWT解析器
        JwtParser parser = Jwts.parser().verifyWith(secretKey).build();
        try {
            return parser.parseSignedClaims(token).getPayload();
        } catch (Exception e) {
            log.error("token 解析失败", e);
            throw new ValidationFailureException("token 解析失败");
        }
    }

    /**
     * 根据身份信息获取键值
     *
     * @param claims 身份信息
     * @param key    键
     * @return 值
     */
    public static String getClaimsValue(Claims claims, String key) {
        return String.valueOf(claims.get(key));
    }

    /**
     * 验证token是否需要续费
     *
     * @param onlineUser 登录用户
     * @return 令牌
     */
    public static boolean isNeedToRefresh(@NonNull OnlineUser onlineUser) {
        // 过期时间
        LocalDateTime expireTime = onlineUser.getExpireTime();
        // 登录时间
        LocalDateTime loginTime = onlineUser.getLoginTime();
        // 当前时间
        LocalDateTime currentTime = LocalDateTime.now();
        // 计算有效时间
        Duration duration = Duration.between(loginTime, expireTime);

        long effectiveTime = duration.toMillis();
        // 计算已经消耗的有效时间
        Duration timeSpent = Duration.between(loginTime, currentTime);

        return ((double) effectiveTime / timeSpent.toMillis()) < 2;
    }
}
