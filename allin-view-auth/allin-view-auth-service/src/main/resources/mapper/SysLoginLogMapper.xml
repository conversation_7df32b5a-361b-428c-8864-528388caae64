<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allin.view.auth.mapper.SysLoginLogMapper">
  <resultMap id="BaseResultMap" type="com.allin.view.auth.pojo.entity.SysLoginLog">
    <!--@mbg.generated-->
    <!--@Table sys_login_log-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="login_time" jdbcType="TIMESTAMP" property="loginTime" />
    <result column="login_ip" jdbcType="VARCHAR" property="loginIp" />
    <result column="browser" jdbcType="VARCHAR" property="browser" />
    <result column="browser_version" jdbcType="VARCHAR" property="browserVersion" />
    <result column="os" jdbcType="VARCHAR" property="os" />
    <result column="os_version" jdbcType="VARCHAR" property="osVersion" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="message" jdbcType="VARCHAR" property="message" />
  </resultMap>
</mapper>