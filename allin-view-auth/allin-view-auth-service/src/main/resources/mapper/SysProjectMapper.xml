<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allin.view.auth.mapper.SysProjectMapper">

    <resultMap id="BaseResultMap" type="com.allin.view.auth.pojo.entity.SysProject">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="projectName" column="project_name" jdbcType="VARCHAR"/>
        <result property="projectLogo" column="project_logo" jdbcType="VARCHAR"/>
        <result property="projectBackgroundImage" column="project_background_image" jdbcType="VARCHAR"/>
        <result property="projectCode" column="project_code" jdbcType="VARCHAR"/>
        <result property="projectType" column="project_type" jdbcType="VARCHAR"/>
        <result property="deptId" column="dept_id" jdbcType="VARCHAR"/>
        <result property="ip" column="ip" jdbcType="VARCHAR"/>
        <result property="port" column="port" jdbcType="INTEGER"/>
        <result property="projectUrl" column="project_url" jdbcType="VARCHAR"/>
        <result property="projectStatus" column="project_status" jdbcType="TINYINT"/>
        <result property="projectDescription" column="project_description" jdbcType="VARCHAR"/>
        <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
    </resultMap>

    <!--拷贝此句代码-->
    <select id="getProjectPage" resultType="com.allin.view.auth.pojo.vo.SysProjectVo">
        select tb1.* from sys_project tb1
        <where>
            tb1.deleted = 0
            <if test="query.projectName != null and query.projectName != ''">
                and tb1.project_name like concat('%',#{query.projectName}, '%')
            </if>
            <if test="query.projectLogo != null and query.projectLogo != ''">
                and tb1.project_logo like concat('%',#{query.projectLogo}, '%')
            </if>
            <if test="query.projectCode != null and query.projectCode != ''">
                and tb1.project_code like concat('%',#{query.projectCode}, '%')
            </if>
            <if test="query.projectType != null and query.projectType != ''">
                and tb1.project_type = #{query.projectType}
            </if>
            <if test="query.deptId != null and query.deptId != ''">
                and tb1.dept_id = #{query.deptId}
            </if>
            <if test="query.ip != null and query.ip != ''">
                and tb1.ip like concat('%',#{query.ip}, '%')
            </if>
            <if test="query.port != null">
                and tb1.port = #{query.port}
            </if>
            <if test="query.projectUrl != null and query.projectUrl != ''">
                and tb1.project_url like concat('%',#{query.projectUrl}, '%')
            </if>
            <if test="query.projectStatus != null">
                and tb1.project_status = #{query.projectStatus}
            </if>
            <if test="query.projectDescription != null and query.projectDescription != ''">
                and tb1.project_description like concat('%',#{query.projectDescription}, '%')
            </if>
            <if test="query.remarks != null and query.remarks != ''">
                and tb1.remarks like concat('%',#{query.remarks}, '%')
            </if>
            <if test="query.filterManagementCenter == 1">
                and tb1.project_type != 'allin-management-center'
            </if>
            ${query.params.dataScope}
        </where>
        order by tb1.updated_time desc
    </select>
</mapper>
