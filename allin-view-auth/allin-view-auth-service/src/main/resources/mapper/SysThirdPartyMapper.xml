<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allin.view.auth.mapper.SysThirdPartyMapper">
    <resultMap id="BaseResultMap" type="com.allin.view.auth.pojo.entity.ApiKey">
        <!--@mbg.generated-->
        <!--@Table sys_third_party-->
        <id column="id" jdbcType="CHAR" property="id"/>
        <result column="service_name" jdbcType="VARCHAR" property="serviceName"/>
        <result column="token" jdbcType="VARCHAR" property="token"/>
        <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime"/>
        <result column="enabled" jdbcType="INTEGER" property="enabled"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
    </resultMap>
</mapper>