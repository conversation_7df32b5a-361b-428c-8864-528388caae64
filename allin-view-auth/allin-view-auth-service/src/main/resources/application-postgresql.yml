spring:
  servlet:
    multipart:
      max-request-size: 1024MB
      max-file-size: 1024MB
  application:
    name: allin-view-auth
  main:
    allow-bean-definition-overriding: true
  flyway:
    enabled: false
  datasource:
    #url: jdbc:postgresql://**************:5432/allin_view_auth
    url: jdbc:postgresql://**************:5432/auth
    username: postgres
    password: Allin@019
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 5
      min-idle: 10
      max-active: 20
      keep-alive: true
      min-evictable-idle-time-millis: 600000
      max-evictable-idle-time-millis: 900000
      time-between-eviction-runs-millis: 2000
      max-wait: 1200
      validation-query: SELECT 'x'
      validation-query-timeout: 60
      test-while-idle: true
      filters: stat
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: administrator
        login-password: <PERSON><PERSON>@022
        allow: ""
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 5000
  data:
    redis:
      host: **************
      port: 6379
      password: Allin@2022
      timeout: 3s
      database: 10
  messages:
    basename: i18n.allin_view_auth

server:
  port: 8081

allin:
  view:
    auth:
      api-whites:
        - /captcha/**
        - /token/**
      allow-multiple-login: true
      ip-whites:
        - 192.168.77.*
