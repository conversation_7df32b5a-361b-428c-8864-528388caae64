spring:
  servlet:
    multipart:
      max-request-size: 1024MB
      max-file-size: 1024MB
  application:
    name: allin-view-auth
  flyway:
    enabled: false
  datasource:
    url: *********************************************************************************************************************************************************************************************************************
    username: allin_view_auth
    password: Wlb2008!@
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 5
      min-idle: 10
      max-active: 20
      keep-alive: true
      min-evictable-idle-time-millis: 600000
      max-evictable-idle-time-millis: 900000
      time-between-eviction-runs-millis: 2000
      max-wait: 1200
      validation-query: SELECT 'x'
      validation-query-timeout: 60
      test-while-idle: true
      filters: stat
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: administrator
        login-password: Allin@022
        allow: ""
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 10000
  data:
    redis:
      host: ************
      port: 6379
      password: Allin@2022
      timeout: 3s
      database: 14
  messages:
    basename: i18n.allin_view_auth

server:
  port: 8085

allin:
  view:
    auth:
      allow-multiple-login: true