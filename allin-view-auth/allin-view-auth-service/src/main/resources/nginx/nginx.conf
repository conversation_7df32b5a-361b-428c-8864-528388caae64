# 此处为auth服务的标准nginx部署配置，请根据实际情况修改监听的端口和转发的IP和端口
# 请将该文件放在nginx配置目录conf.d下，并在nginx.conf加上配置: include /etc/nginx/conf.d/*.conf;
server {
    listen      8082;
    server_name localhost;
    charset     utf-8;
    root        /home/<USER>/project/allin-auth-4x/dist;
    index       index.html                             index.htm;

    location / {
        root      /home/<USER>/project/allin-auth-4x/dist;
        try_files $uri                                   $uri/ /index.html;
    }

    location /prod-api/ {
        client_max_body_size 100m;
        rewrite              ^/prod-api/(.*)       /$1                        break;
        proxy_set_header     Host                  $http_host;
        proxy_set_header     X-Real-IP             $remote_addr;
        proxy_set_header     REMOTE-HOST           $remote_addr;
        proxy_set_header     X-Forwarded-For       $proxy_add_x_forwarded_for;
        proxy_pass           http://127.0.0.1:8081;
    }

    location /socket/ {
        rewrite            ^/socket/(.*)         /$1           break;
        proxy_set_header   Upgrade               $http_upgrade;
        proxy_set_header   Connection            "upgrade";
        proxy_pass         http://127.0.0.1:8081;
        proxy_http_version 1.1;
        proxy_read_timeout 3600s;
        proxy_send_timeout 3600s;
    }
}