create table if not exists sys_third_party
(
    id           varchar(19)   not null comment '主键ID'
        primary key,
    service_name varchar(255)  not null comment '服务名称',
    token        varchar(255)  not null comment '令牌',
    expire_time  datetime      null comment '过期时间',
    enabled      int default 1 not null comment '是否启用 (1-启用, 0-禁用)',
    project_id   varchar(36)   not null comment '项目id',
    created_by   varchar(255)  not null comment '创建人',
    created_time datetime      not null comment '创建时间',
    updated_by   varchar(255)  null comment '更新人',
    updated_time datetime      null comment '更新时间'
)
    comment '第三方令牌表';