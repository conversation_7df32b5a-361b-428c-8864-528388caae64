/*
Navicat MySQL Data Transfer

Source Server         : LOCAL
Source Server Version : 50744
Source Host           : localhost:3306
Source Database       : flowable7

Target Server Type    : MYSQL
Target Server Version : 50744
File Encoding         : 65001

Date: 2024-03-14 14:13:28
*/

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for sys_depart
-- ----------------------------
DROP TABLE IF EXISTS `sys_depart`;
CREATE TABLE `sys_depart` (
                              `id` varchar(19) NOT NULL COMMENT '主键',
                              `parent_id` varchar(19) NULL COMMENT '父部门(根节点的父为null）',
                              `dept_name` varchar(120) NOT NULL COMMENT '部门名称',
                              `dept_code` varchar(64) NOT NULL COMMENT '部门编号',
                              `dept_type` tinyint(10) NOT NULL COMMENT '部门类型（0：机场集团 1：分公司 2：机场 3：部门）',
                              `dept_status` tinyint(1) NOT NULL COMMENT '部门状态 1启用 0禁用',
                              `leader_id` varchar(19) DEFAULT NULL COMMENT '负责人主键',
                              `dept_logo` varchar(19) DEFAULT NULL COMMENT '部门logo',
                              `dept_primary_title` varchar(120) DEFAULT NULL COMMENT '部门主标题',
                              `dept_second_title` varchar(120) DEFAULT NULL COMMENT '部门副标题',
                              `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
                              `remarks` varchar(50) DEFAULT NULL COMMENT '备注',
                              `created_by` varchar(19) DEFAULT NULL COMMENT '创建人',
                              `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                              `updated_by` varchar(19) DEFAULT NULL COMMENT '更新人',
                              `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                              `deleted` tinyint(1) DEFAULT NULL COMMENT '删除标志(0表示存在，1表示删除)',
                              `tenant_id` varchar(19) DEFAULT NULL COMMENT '租户ID',
                              PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='部门表';

-- ----------------------------
-- Table structure for sys_project
-- ----------------------------
DROP TABLE IF EXISTS `sys_project`;
CREATE TABLE `sys_project` (
                               `id` varchar(19) NOT NULL COMMENT '主键',
                               `project_name` varchar(120) NOT NULL COMMENT '项目名称',
                               `project_logo` varchar(19) NOT NULL COMMENT '项目logo',
                               `project_background_image` varchar(19) NOT NULL COMMENT '项目背景图',
                               `project_code` varchar(64) NOT NULL COMMENT '项目编码',
                               `project_type` varchar(50) NOT NULL COMMENT '项目类型(围界: allin-security 鸟击：allin-bird 跑道：allin-runaway AI-视频持久监控: allin-ai-detection 管理中心: allin-management-center)',
                               `dept_id` varchar(19) NOT NULL COMMENT '部门主键',
                               `ip` varchar(20) NOT NULL COMMENT 'ip',
                               `port` int(11) NOT NULL COMMENT '端口',
                               `project_url` varchar(200) NOT NULL COMMENT '项目访问链接',
                               `project_status` tinyint(1) NOT NULL COMMENT '项目状态 1启用 0禁用',
                               `project_description` varchar(200) NULL COMMENT '项目描述',
                               `remarks` varchar(50) DEFAULT NULL COMMENT '备注',
                               `created_by` varchar(19) DEFAULT NULL COMMENT '创建人',
                               `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                               `updated_by` varchar(19) DEFAULT NULL COMMENT '更新人',
                               `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                               `deleted` tinyint(1) DEFAULT NULL COMMENT '删除标志(0表示存在，1表示删除)',
                               `tenant_id` varchar(19) DEFAULT NULL COMMENT '租户ID',
                               PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目表';

-- ----------------------------
-- Table structure for sys_project_member
-- ----------------------------
DROP TABLE IF EXISTS `sys_project_member`;
CREATE TABLE `sys_project_member` (
                                      `id` varchar(19) NOT NULL COMMENT '主键',
                                      `user_id` varchar(19) NOT NULL COMMENT '用户主键',
                                      `project_id` varchar(19) NOT NULL COMMENT '项目主键',
                                      `remarks` varchar(50) DEFAULT NULL COMMENT '备注',
                                      `created_by` varchar(19) DEFAULT NULL COMMENT '创建人',
                                      `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `updated_by` varchar(19) DEFAULT NULL COMMENT '更新人',
                                      `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                      `deleted` tinyint(1) DEFAULT NULL COMMENT '删除标志(0表示存在，1表示删除)',
                                      PRIMARY KEY (`id`),
                                      KEY `idx_project_user_id` (`user_id`) USING BTREE,
                                      KEY `idx_project_id` (`project_id`,`user_id`) USING BTREE,
                                      CONSTRAINT `fk_project_id` FOREIGN KEY (`project_id`) REFERENCES `sys_project` (`id`),
                                      CONSTRAINT `fk_project_user_id` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目成员表';

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role` (
                            `id` varchar(19) NOT NULL COMMENT '主键',
                            `role_name` varchar(120) NOT NULL COMMENT '角色名称',
                            `is_enable` tinyint(1) NOT NULL COMMENT '是否启用， 0：禁用；1：启用',
                            `is_build_in` tinyint(1) DEFAULT NULL COMMENT '是否内置（1是 0否）',
                            `type` varchar(50) NOT NULL COMMENT '类型，区分不同项目类型（(围界: allin-security 鸟击：allin-bird 跑道：allin-runaway AI-视频持久监控: allin-ai-detection 管理中心: allin-management-center）',
                            `sort` int(11) DEFAULT '1' COMMENT '排序',
                            `project_id` varchar(19) DEFAULT NULL COMMENT '项目ID',
                            `remarks` varchar(50) DEFAULT NULL COMMENT '备注',
                            `created_by` varchar(19) DEFAULT NULL COMMENT '创建人',
                            `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                            `updated_by` varchar(19) DEFAULT NULL COMMENT '更新人',
                            `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                            `deleted` tinyint(1) DEFAULT NULL COMMENT '删除标志(0表示存在，1表示删除)',
                            `tenant_id` varchar(19) DEFAULT NULL COMMENT '租户ID',
                            PRIMARY KEY (`id`),
                            KEY `fk_role_project_id` (`project_id`),
                            CONSTRAINT `fk_role_project_id` FOREIGN KEY (`project_id`) REFERENCES `sys_project` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统角色表';

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu` (
                                 `id` varchar(19) NOT NULL COMMENT '主键',
                                 `role_id` varchar(19) NOT NULL COMMENT '角色主键',
                                 `menu` varchar(100) NOT NULL COMMENT '菜单标识符',
                                 `created_by` varchar(19) DEFAULT NULL COMMENT '创建人',
                                 `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                 `updated_by` varchar(19) DEFAULT NULL COMMENT '更新人',
                                 `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                 `deleted` tinyint(1) DEFAULT NULL COMMENT '删除标志(0表示存在，1表示删除)',
                                 PRIMARY KEY (`id`),
                                 KEY `idx_role_id` (`role_id`) USING BTREE,
                                 CONSTRAINT `fk_role_menu_id` FOREIGN KEY (`role_id`) REFERENCES `sys_role` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色菜单表';

-- ----------------------------
-- Table structure for sys_role_perm
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_perm`;
CREATE TABLE `sys_role_perm` (
                                 `id` varchar(19) NOT NULL COMMENT '主键',
                                 `role_id` varchar(19) NOT NULL COMMENT '角色主键',
                                 `permission` varchar(100) NOT NULL COMMENT '权限标识符',
                                 `created_by` varchar(19) DEFAULT NULL COMMENT '创建人',
                                 `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                 `updated_by` varchar(19) DEFAULT NULL COMMENT '更新人',
                                 `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                 `deleted` tinyint(1) DEFAULT NULL COMMENT '删除标志(0表示存在，1表示删除)',
                                 PRIMARY KEY (`id`),
                                 KEY `idx_role_id` (`role_id`) USING BTREE,
                                 CONSTRAINT `fk_role_perm_id` FOREIGN KEY (`role_id`) REFERENCES `sys_role` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限表';

-- ----------------------------
-- Table structure for sys_role_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_user`;
CREATE TABLE `sys_role_user` (
                                 `id` varchar(19) NOT NULL COMMENT '主键',
                                 `role_id` varchar(19) NOT NULL COMMENT '角色主键',
                                 `user_id` varchar(19) NOT NULL COMMENT '用户主键',
                                 `created_by` varchar(19) DEFAULT NULL COMMENT '创建人',
                                 `created_time` datetime DEFAULT NULL COMMENT '创建时间',
                                 `updated_by` varchar(19) DEFAULT NULL COMMENT '更新人',
                                 `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
                                 `deleted` tinyint(1) DEFAULT NULL COMMENT '删除标志(0表示存在，1表示删除)',
                                 PRIMARY KEY (`id`),
                                 UNIQUE KEY `idx_role_user` (`role_id`,`user_id`) USING BTREE,
                                 KEY `idx_role_user_id` (`user_id`) USING BTREE,
                                 CONSTRAINT `fk_role_user_1` FOREIGN KEY (`role_id`) REFERENCES `sys_role` (`id`),
                                 CONSTRAINT `fk_role_user_2` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统用户表';
