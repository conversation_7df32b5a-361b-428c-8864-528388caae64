package com.allin.view.auth.controller;

import cn.hutool.core.collection.CollUtil;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.auth.pojo.dto.*;
import com.allin.view.auth.pojo.entity.SysUser;
import com.allin.view.auth.pojo.query.SysUserQuery;
import com.allin.view.auth.pojo.vo.OnlineUserVo;
import com.allin.view.auth.pojo.vo.SysUserVo;
import com.allin.view.auth.safety.constant.CacheKeyConstants;
import com.allin.view.auth.safety.manager.TokenManager;
import com.allin.view.auth.safety.pojo.dto.OnlineUser;
import com.allin.view.auth.service.SysUserService;
import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.PageParam;
import com.allin.view.base.domain.Result;
import com.allin.view.base.execl.EasyExcelOperUtils;
import com.allin.view.config.cache.CacheService;
import com.allin.view.config.redis.annotation.PreventDuplicateSubmit;
import com.allin.view.log.annotation.Log;
import com.allin.view.ws.handler.CustomParamWebSocketHandler;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 成员管理
 *
 * <AUTHOR>
 * @date 2024/3/5
 */
@Validated
@RestController
@RequestMapping("/user")
public class UserController {

    private final SysUserService userService;

    private final CacheService redisService;

    private final CustomParamWebSocketHandler webSocketHandler;

    private final TokenManager tokenManager;

    public UserController(SysUserService userService,
                          CacheService redisService,
                          CustomParamWebSocketHandler webSocketHandler,
                          TokenManager tokenManager) {
        this.userService = userService;
        this.redisService = redisService;
        this.webSocketHandler = webSocketHandler;
        this.tokenManager = tokenManager;
    }

    /**
     * 查询用户列表
     */
    @GetMapping
    @Log(title = "用户管理", operDesc = "查询用户列表")
    public Result<PageData<SysUserVo>> getUserList(PageParam pageParam, @Valid SysUserQuery sysUserQuery) {
        Page<SysUserVo> page = pageParam.toPage();
        userService.getUserList(page, sysUserQuery);
        PageData<SysUserVo> instance = PageData.getInstance(page);
        return Result.ok(instance);
    }

    /**
     * 删除用户
     *
     * @param userId 用户唯一标识
     */
    @DeleteMapping("/{userId}")
    @Log(title = "用户管理", operDesc = "删除用户")
    public Result<String> deleteUser(@PathVariable String userId) {
        return userService.deleteUser(userId) ? Result.ok() : Result.fail();
    }

    /**
     * 禁用/启用用户
     *
     * @param userId 用户唯一标识
     * @param status 状态 1：启用 0：禁用
     */
    @PutMapping("/status/{userId}/{status}")
    @Log(title = "用户管理", operDesc = "禁用/启用用户")
    public Result<String> editUserStatus(@PathVariable String userId, @PathVariable Integer status) {
        return userService.editUserStatus(userId, status) ? Result.ok() : Result.fail();
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/detail")
    @Log(title = "用户管理", operDesc = "获取当前用户信息")
    public Result<SysUserVo> getUserInfo() {
        return Result.ok(userService.getUserInfo(SecurityContextHolder.getUserId()));
    }

    /**
     * 新增用户
     */
    @PostMapping
    @PreventDuplicateSubmit
    @Log(title = "用户管理", operDesc = "新增用户")
    public Result<String> addUser(@RequestBody @Validated AddUserDto addUserDto) {
        boolean add = userService.addUser(addUserDto);
        if (!add) {
            return Result.fail();
        }
        return Result.ok();
    }

    /**
     * 编辑用户
     */
    @PutMapping
    @Log(title = "用户管理", operDesc = "编辑用户")
    public Result<String> editUser(@RequestBody @Validated EditUserDto editUserDto) {
        boolean add = userService.editUser(editUserDto);
        if (!add) {
            return Result.fail();
        }
        return Result.ok();
    }

    /**
     * 重置密码
     */
    @PutMapping("/password")
    @Log(title = "用户管理", operDesc = "重置密码")
    public Result<String> editPassword(@RequestBody @Validated EditPasswordDto editPasswordDto) {
        return userService.updatePassword(editPasswordDto) ? Result.ok() : Result.fail();
    }

    /**
     * 由超级管理员重置密码
     */
    @PutMapping("/password/by_admin")
    @Log(title = "用户管理", operDesc = "由超级管理员重置密码")
    public Result<String> editPasswordByAdmin(@RequestBody @Validated EditPasswordByAdminDto editPasswordDto) {
        return userService.updatePassword(editPasswordDto) ? Result.ok() : Result.fail();
    }

    /**
     * 在线用户列表
     */
    @GetMapping("/online/list")
    public Result<List<OnlineUserVo>> getOnlineUserList() {
        final List<OnlineUser> onlineUsers = tokenManager.listOnlineUser();
        List<OnlineUserVo> onlineUserVos = new ArrayList<>();
        for (OnlineUser onlineUser : onlineUsers) {
            final SysUser user = userService.getByUserId(onlineUser.getUserId());
            if (user == null) {
                continue;
            }

            final OnlineUserVo onlineUserVo = new OnlineUserVo();
            onlineUserVo.setId(onlineUser.getUserId());
            onlineUserVo.setLoginTime(onlineUser.getLoginTime());
            onlineUserVo.setExpireTime(onlineUser.getExpireTime());
            onlineUserVo.setLoginIp(onlineUser.getLoginIp());
            onlineUserVo.setClientType(onlineUser.getClientType());

            onlineUserVo.setFullName(user.getFullName());
            onlineUserVo.setUserName(user.getUserName());
            onlineUserVo.setHeadImg(user.getHeadImg());
            onlineUserVos.add(onlineUserVo);
        }
        return Result.ok(onlineUserVos);
    }

    /**
     * 强制下线
     */
    @PostMapping("/force/offline")
    public Result<Void> forceOffline(@RequestBody @Validated UserIdDto userIdDto) {
        webSocketHandler.getSessionManager().remove(userIdDto.getUserId());
        final Set<String> userCacheKeys = redisService.scanKeys(CacheKeyConstants.getTokenCacheKeyPattern(userIdDto.getUserId()));
        if (CollUtil.isNotEmpty(userCacheKeys)) {
            userCacheKeys.forEach(redisService::deleteObject);
        }
        return Result.ok();
    }


    /**
     * 导出
     */
    @GetMapping("/export")
    @Log(title = "用户管理", operDesc = "导出")
    public void export(@Valid SysUserQuery sysUserQuery, HttpServletResponse response) {
        Page<SysUserVo> page = Page.of(-1, -1);
        userService.getUserList(page, sysUserQuery);
        EasyExcelOperUtils.exportXlsx(response, page.getRecords(), SysUserVo.class, "用户表");
    }

}
