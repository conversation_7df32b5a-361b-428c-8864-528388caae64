package com.allin.view.auth.safety.pojo.dto;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.jwt.JWTUtil;
import com.allin.view.auth.constant.TokenConstants;
import com.allin.view.auth.pojo.entity.SysUser;
import com.allin.view.auth.safety.enums.ClientTypeEnums;
import com.allin.view.base.enums.base.IEnums;
import com.allin.view.base.exception.service.ValidationFailureException;
import com.allin.view.base.i18n.I18nUtil;
import lombok.Data;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

/**
 * JWT 结构的 Token
 *
 * <AUTHOR>
 * @since 2025/7/21
 */
@Data
public class JwtToken {

    /**
     * 签发人
     */
    private String iss = "allin-view-auth-4.x";

    /**
     * 主题
     */
    private String sub;

    /**
     * 受众
     */
    private String aud = "*";

    /**
     * 过期时间 = 签发时间 + 有效时长
     */
    private Long exp;

    /**
     * 生效时间, 默认为当前时间
     */
    private Long nbf;

    /**
     * 签发时间, 默认为当前时间
     */
    private Long iat;

    /**
     * 刷新令牌id
     */
    private String refresh_id;

    /**
     * 令牌的唯一标识
     */
    private String jti;

    /**
     * 客户端类型
     */
    private String client_type;

    /**
     * 用户唯一标识
     */
    private String user_id;

    /**
     * 用户账号
     */
    private String user_name;

    /**
     * 姓名
     */
    private String full_name;

    /**
     * 头像
     */
    private String head_img;

    /**
     * 性别(男, 女)
     */
    private String sex;

    /**
     * 部门id
     */
    private String dept_id;

    /**
     * 是否为超级管理员
     */
    private Boolean is_super_admin;

    public JwtToken() {
    }

    public JwtToken(SysUser user) {
        this(user, IdUtil.fastSimpleUUID());
    }

    /**
     * 构造方法
     *
     * @param user       用户信息
     * @param refresh_id 刷新令牌
     */
    public JwtToken(SysUser user, String refresh_id) {
        final long currentTimeMillis = System.currentTimeMillis();

        this.refresh_id = refresh_id;
        this.jti = refresh_id;

        this.nbf = currentTimeMillis;
        this.iat = currentTimeMillis;
        // 默认有效时间5分钟
        this.exp = currentTimeMillis + TimeUnit.MILLISECONDS.toMinutes(5);

        this.sub = user.getId();
        this.user_id = user.getId();
        this.user_name = user.getUserName();
        this.full_name = user.getFullName();
        this.head_img = user.getHeadImg();
        this.sex = user.getSex();
        this.dept_id = user.getDeptId();
        this.is_super_admin = user.getIsSuperAdmin();
    }

    public static JwtToken parse(String jwtToken) {
        return JWTUtil.parseToken(jwtToken).getPayloads().toBean(JwtToken.class);
    }

    public static void verify(String jwtToken) {
        // 校验签名
        if (JWTUtil.verify(jwtToken, TokenConstants.SECRET.getBytes(StandardCharsets.UTF_8))) {
            throw new ValidationFailureException(401, I18nUtil.isParamException("token"));
        }
    }

    public String toStr() {
        return JWTUtil.createToken(BeanUtil.beanToMap(this),
                TokenConstants.SECRET.getBytes(StandardCharsets.UTF_8));

    }

    public ClientTypeEnums clientTypeEnum() {
        return IEnums.tryFindByCode(ClientTypeEnums.class, client_type)
                .orElseThrow(() -> new ValidationFailureException("客户端类型不合法"));
    }
}
