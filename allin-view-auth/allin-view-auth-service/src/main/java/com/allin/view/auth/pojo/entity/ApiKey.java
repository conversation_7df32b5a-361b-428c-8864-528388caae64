package com.allin.view.auth.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 第三方令牌表
 *
 * <AUTHOR>
 * @since 2025/3/14
 */
@Data
@TableName(value = "api_key")
public class ApiKey {
    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 服务名称
     */
    @TableField(value = "service_name")
    private String serviceName;

    /**
     * 令牌
     */
    @TableField(value = "token")
    private String token;

    /**
     * 权限字符串, 多个之间逗号隔开
     */
    @TableField(value = "permissions")
    private String permissions;

    /**
     * 过期时间
     */
    @TableField(value = "expire_time")
    private LocalDateTime expireTime;

    /**
     * 是否启用 (1-启用, 0-禁用)
     */
    @TableField(value = "enabled")
    private Integer enabled;

    /**
     * 创建人
     */
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT)
    private LocalDateTime updatedTime;
}