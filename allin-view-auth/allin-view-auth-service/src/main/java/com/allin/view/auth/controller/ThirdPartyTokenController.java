package com.allin.view.auth.controller;

import com.allin.view.auth.api.ThirdPartyTokenApi;
import com.allin.view.auth.interceptor.annotation.WhiteListApi;
import com.allin.view.auth.pojo.entity.SysThirdParty;
import com.allin.view.auth.pojo.vo.ThirdPartyTokenVo;
import com.allin.view.auth.service.SysThirdPartyService;
import com.allin.view.base.domain.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

/**
 * 第三方令牌控制器
 *
 * <AUTHOR>
 * @date 2024/5/30
 */
@Slf4j
@RestController
public class ThirdPartyTokenController implements ThirdPartyTokenApi {

    private final SysThirdPartyService thirdPartyService;

    public ThirdPartyTokenController(SysThirdPartyService thirdPartyService) {
        this.thirdPartyService = thirdPartyService;
    }

    @WhiteListApi
    @Override
    public Result<ThirdPartyTokenVo> check(String token, String path) {
        // 校验令牌
        SysThirdParty thirdParty = thirdPartyService.checkToken(token, path);
        final ThirdPartyTokenVo vo = new ThirdPartyTokenVo();
        BeanUtils.copyProperties(thirdParty, vo);
        return Result.ok(vo);
    }
}