package com.allin.view.auth.controller;

import com.allin.view.auth.pojo.dto.AddSysRoleUserDto;
import com.allin.view.auth.pojo.query.SysRoleUserQuery;
import com.allin.view.auth.pojo.vo.SysRoleUserVo;
import com.allin.view.auth.service.SysRoleUserService;
import com.allin.view.base.domain.PageData;
import com.allin.view.base.domain.PageParam;
import com.allin.view.base.domain.Result;
import com.allin.view.config.redis.annotation.PreventDuplicateSubmit;
import com.allin.view.log.annotation.Log;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 角色用户表
 * <AUTHOR>
 * @since 2024/03/14 14:05:04
 */
@RestController
@Validated
@Slf4j
@RequestMapping("sys_role_user")
public class SysRoleUserController {

    private final SysRoleUserService sysRoleUserService;

    public SysRoleUserController(SysRoleUserService sysRoleUserService) {
        this.sysRoleUserService = sysRoleUserService;
    }

    /**
     * 新增角色用户表
     */
    @PostMapping
    @PreventDuplicateSubmit
    @Log(title = "角色用户表", operDesc = "新增角色用户表")
    public Result<String> createUser(@Validated @RequestBody List<AddSysRoleUserDto> roleUserDtoList) {
        boolean save = sysRoleUserService.createRoleUser(roleUserDtoList);
        if (!save) {
            return Result.fail();
        }
        return Result.ok();
    }


    /**
     * 删除角色用户表
     */
    @DeleteMapping("{id}")
    @Log(title = "角色用户表", operDesc = "删除角色用户表")
    public Result<String> deleteUser(@PathVariable String id) {
        boolean delete = sysRoleUserService.deleteRoleUser(id);
        if (!delete) {
            return Result.fail();
        }
        return Result.ok();
    }

    /**
     * 分页查询角色用户表
     */
    @GetMapping
    @Log(title = "角色用户表", operDesc = "分页查询角色用户表")
    public Result<PageData<SysRoleUserVo>> getUserPage(
            PageParam pageParam,
            @Valid SysRoleUserQuery sysRoleUserQuery) {
        Page<SysRoleUserVo> page = pageParam.toPage();
        sysRoleUserService.getRoleUserPage(page, sysRoleUserQuery);
        PageData<SysRoleUserVo> instance = PageData.getInstance(page);
        return Result.ok(instance);
    }

    /**
     * 查询详情角色用户表
     */
    @GetMapping("{id}")
    @Log(title = "角色用户表", operDesc = "查询角色用户表")
    public Result<SysRoleUserVo> getUser(@PathVariable String id) {
        SysRoleUserVo sysRoleUserVo = sysRoleUserService.getRoleUser(id);
        return Result.ok(sysRoleUserVo);
    }

}
