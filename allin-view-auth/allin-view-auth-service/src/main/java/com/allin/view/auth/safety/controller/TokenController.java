package com.allin.view.auth.safety.controller;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.allin.view.auth.api.TokenApi;
import com.allin.view.auth.interceptor.annotation.WhiteListApi;
import com.allin.view.auth.pojo.dto.LoginBody;
import com.allin.view.auth.pojo.dto.TokenDto;
import com.allin.view.auth.pojo.entity.SysUser;
import com.allin.view.auth.safety.constant.CacheKeyConstants;
import com.allin.view.auth.safety.enums.ClientTypeEnums;
import com.allin.view.auth.safety.manager.TokenManager;
import com.allin.view.auth.safety.pojo.dto.JwtToken;
import com.allin.view.auth.safety.pojo.vo.LoginVo;
import com.allin.view.auth.safety.utils.SecurityUtils;
import com.allin.view.auth.service.SysLoginLogService;
import com.allin.view.auth.service.SysUserService;
import com.allin.view.auth.utils.TokenUtils;
import com.allin.view.base.domain.Result;
import com.allin.view.base.exception.service.ValidationFailureException;
import com.allin.view.config.cache.CacheService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 认证授权
 *
 * <AUTHOR>
 * @date 2024/2/28
 */
@Slf4j
@RestController
public class TokenController implements TokenApi {

    /**
     * 密码错误次数过期时间, 单位秒
     */
    public static final int PASSWORD_FAIL_EXPIRATION_TIME = 120;

    /**
     * 密码错误锁定时间，单位分钟
     */
    public static final int PASSWORD_LOCK_TIME = 10;

    /**
     * 密码错误可重试次数
     */
    private static final int PASSWORD_RETRY_COUNT = 5;


    private final SysUserService userService;

    private final CacheService redisService;

    private final SysLoginLogService loginLogService;

    private final TokenManager tokenManager;
    public TokenController(SysUserService userService,
                           CacheService redisService,
                           SysLoginLogService loginLogService,
                           TokenManager tokenManager) {
        this.userService = userService;
        this.redisService = redisService;
        this.loginLogService = loginLogService;
        this.tokenManager = tokenManager;
    }

    @Override
    public Result<String> refreshToken(@RequestBody @Validated TokenDto token) {
        // 创建新 token
        final String newToken = tokenManager.doRefreshToken(token.getToken());
        return Result.ok(newToken);
    }

    /**
     * 登录
     */
    @WhiteListApi
    @PostMapping(value = {"/login", "/login/app"})
    public Result<LoginVo> login(@RequestBody LoginBody loginBody, HttpServletRequest request) {
        // 兼容旧请求
        if (loginBody.getClientType() == null) {
            ClientTypeEnums clientType = request.getRequestURI().contains("/login/app")
                    ? ClientTypeEnums.APP : ClientTypeEnums.PC;
            // 判断请求路径是否包含"/login/app"
            loginBody.setClientType(clientType.getCode());
        }

        // 登录前校验
        loginBeforeVerification(loginBody);
        SysUser user = userService.getByUserName(loginBody.getUserName());
        // 登录后校验
        loginAfterVerification(loginBody, user);

        // 创建jwtToken
        final JwtToken jwtToken = new JwtToken(user);

        // 缓存登录信息
        tokenManager.setOnlineUser(jwtToken);

        // 强制下线其他
        tokenManager.forceLogoutOther(user, loginBody);

        // 记录登录日志
        loginLogService.recordSuccessLog(user.getId(), "登录成功");
        return Result.ok(LoginVo.of(jwtToken, user.isPasswordExpired()));
    }

    /**
     * 临时单点登录
     */
    @WhiteListApi
    @PostMapping("/sso/login")
    public Result<String> loginBySSO(@RequestParam String param) {
        // 1. Base64解密
        byte[] decodedBytes = Base64.decode(param);

        String decryptedStr = SecurityUtils.desDecrypt(new String(decodedBytes));

        // 3. 使用___分割得到用户名和时间戳
        String[] parts = decryptedStr.split("___");
        if (parts.length != 2) {
            return Result.fail("参数无效！");
        }

        String username = parts[0];
        String timestampStr = parts[1];

        // 4. 检验时间戳是否在3秒内
        long timestamp;
        try {
            timestamp = Long.parseLong(timestampStr);
        } catch (NumberFormatException e) {
            return Result.fail("参数无效！");
        }

        if (Math.abs(System.currentTimeMillis() - timestamp) > 15 * 1000) { // 3秒=3000毫秒
            return Result.fail("时间参数过期！");
        }

        // 5. 校验用户名
        if (StrUtil.isEmpty(username)) {
            return Result.fail("用户名无效！");
        }

        SysUser user = userService.getByUserName(username);

        if (ObjectUtil.isEmpty(username)) {
            return Result.fail("用户名无效！");
        }

        // 创建jwtToken
        final JwtToken jwtToken = new JwtToken(user);

        // 缓存登录信息
        tokenManager.setOnlineUser(jwtToken);

        // 记录登录日志
        loginLogService.recordSuccessLog(user.getId(), "单点登录成功");

        return Result.ok(jwtToken.toStr());
    }

    /**
     * 退出登录
     */
    @PostMapping("/logout")
    public Result<String> logout(HttpServletRequest request) {
        String token = TokenUtils.getToken(request);
        if (StrUtil.isNotBlank(token)) {
            tokenManager.logout(token);
        }
        return Result.ok();
    }

    /**
     * 登录前校验
     */
    private void loginBeforeVerification(LoginBody loginBody) {
        String username = loginBody.getUserName();
        String password = loginBody.getPassword();
        // 用户名或密码为空
        if (StringUtils.isAnyBlank(username, password)) {
            throw new ValidationFailureException("用户/密码必须填写！");
        }
        // 验证码为空
        if (StringUtils.isAnyBlank(loginBody.getCaptchaId())) {
            throw new ValidationFailureException("验证码不能为空！");
        }
        // 验证码二次校验
        final String captchaSuccKey = CacheKeyConstants.CAPTCHA_SUCC_KEY.apply(loginBody.getCaptchaId());
        if (redisService.getCacheObject(captchaSuccKey) == null) {
            throw new ValidationFailureException("请先校验验证码！");
        } else {
            redisService.deleteObject(captchaSuccKey);
        }
        // 解密
        loginBody.setPassword(SecurityUtils.desDecrypt(password));
        loginBody.setUserName(SecurityUtils.desDecrypt(username));
    }

    /**
     * 登录后校验
     */
    private void loginAfterVerification(LoginBody loginBody, SysUser user) {
        try {
            final String loginFailLockKey = CacheKeyConstants.LOGIN_FAIL_LOCK.apply(user.getId());
            // 判断是否已锁定
            Integer blackHouse = redisService.getCacheObject(loginFailLockKey);
            if (Objects.nonNull(blackHouse)) {
                throw new ValidationFailureException("此账号已被锁定, 请稍后重试");
            }

            // 判断账号是否禁用
            userService.userVerification(user);

            // 判断密码是否正确
            if (!SecurityUtils.matchesPassword(loginBody.getPassword(), user.getPassword())) {
                final String loginFailCountKey = CacheService
                        .generateKey(CacheKeyConstants.LOGIN_FAIL_COUNT, user.getId());
                // 获取密码错误次数
                Integer calculator = redisService.getCacheObject(loginFailCountKey, 0) + 1;

                // 错误次数达到限制
                if (calculator >= PASSWORD_RETRY_COUNT) {
                    redisService.setCacheObject(loginFailLockKey, 1, PASSWORD_LOCK_TIME, TimeUnit.MINUTES);
                }

                redisService.setCacheObject(loginFailCountKey, calculator, PASSWORD_FAIL_EXPIRATION_TIME, TimeUnit.SECONDS);
                throw new ValidationFailureException(String.format("密码错误%d次, 错误%d次后将被锁定%d分钟!",
                        calculator, PASSWORD_RETRY_COUNT, PASSWORD_LOCK_TIME));
            }
        } catch (ValidationFailureException e) {
            loginLogService.recordFailLog(user.getId(), e.getMessage());
            throw e;
        }
    }
}
