package com.allin.view.auth.pojo.vo;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.format.DateTimeFormat;
import com.allin.view.auth.serializer.annotation.DeptIdToName;
import com.allin.view.base.execl.DefaultExportStyle;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * SysProjectMember 项目成员表展示Vo
 *
 * <AUTHOR>
 * @since 2024/03/14 11:13:08
 */
@Data
public class SysProjectMemberPageVo extends DefaultExportStyle implements Serializable {

    /**
     * 主键
     */
    @ExcelIgnore
    private String id;

    /**
     * 用户主键
     */
    @ExcelProperty(value = "用户主键")
    private String userId;

    /**
     * 用户名
     */
    @ExcelProperty(value = "用户名")
    private String userIdFullName;

    /**
     * 项目主键
     */
    @ExcelProperty(value = "项目主键")
    private String projectId;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remarks;

    /**
     * 部门名称
     */
    @ExcelProperty(value = "部门名称")
    @DeptIdToName(key = "deptName", isAllPath = true)
    private String deptId;

    /**
     * 创建时间
     */
    @DateTimeFormat(value = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "创建时间")
    private LocalDateTime createdTime;

    /**
     * 用户状态
     */
    @ExcelProperty(value = "用户状态")
    private String status;


}
