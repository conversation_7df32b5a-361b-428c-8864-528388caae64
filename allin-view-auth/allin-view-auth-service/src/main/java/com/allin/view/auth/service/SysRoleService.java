package com.allin.view.auth.service;

import com.allin.view.auth.pojo.dto.AddSysRoleDto;
import com.allin.view.auth.pojo.dto.EditSysRoleDto;
import com.allin.view.auth.pojo.entity.SysProject;
import com.allin.view.auth.pojo.entity.SysRole;
import com.allin.view.auth.pojo.query.SysRoleQuery;
import com.allin.view.auth.pojo.vo.ProjectPermissionVo;
import com.allin.view.auth.pojo.vo.SysRoleVo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * @description 针对表【sys_role(系统角色表)】的数据库操作Service
 * <AUTHOR>
 * @since 2024/03/14 10:47:25
 */
public interface SysRoleService extends IService<SysRole> {

    String BUSINESS_TYPE = "sys_role";

    boolean createRole(AddSysRoleDto addSysRoleDto);

    boolean updateRole(EditSysRoleDto editSysRoleDto);

    boolean deleteRole(String id);

    void getRolePage(Page<SysRoleVo> page, SysRoleQuery sysRoleQuery);

    SysRoleVo getRole(String id);

    boolean changeStatus(String id, Integer status);

    void initProjectRole(SysProject sysProject);

    void deleteProjectRole(String id);

    /**
     * 查询当前用户拥有的项目的所有权限
     */
    List<ProjectPermissionVo> getProjectPermissionList(String userId);


    Optional<ProjectPermissionVo> getProjectPermission(String projectId, String userId);

    /**
     * 获取项目角色
     */
    List<SysRole> getProjectRoles(String projectId);
}
