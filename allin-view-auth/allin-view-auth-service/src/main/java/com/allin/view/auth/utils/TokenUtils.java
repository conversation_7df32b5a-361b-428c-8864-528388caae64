package com.allin.view.auth.utils;


import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.allin.view.auth.constant.CacheKeyConstants;
import com.allin.view.auth.constant.ClaimKeyConstants;
import com.allin.view.auth.constant.TokenConstants;
import com.allin.view.base.exception.service.ValidationFailureException;
import com.allin.view.config.redis.service.RedisService;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.SecretKey;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/2/13
 */
@Slf4j
public class TokenUtils extends TokenValidateUtils {

    /**
     * 生成TokenCacheKey
     *
     * @return {@link String}
     * <AUTHOR>
     */
    public static String generateUserCacheKey(String userId) {
        return RedisService.generateKey(CacheKeyConstants.LOGIN_CACHE_KEY, userId, IdUtil.fastSimpleUUID());
    }

    /**
     * 获取 UserCacheKey
     *
     * @param token 用户唯一标识
     */
    public static String getUserCacheKey(String token) {
        final Claims jwt = parseJwt(token);
        final String value = getClaimsValue(jwt, ClaimKeyConstants.USER_CACHE_KEY);
        if (StrUtil.isBlank(value)) {
            throw new ValidationFailureException("无效 token");
        }
        return value;
    }

    /**
     * 根据令牌获取用户标识
     *
     * @return 用户ID
     */
    public static String getUserId(String token) {
        final Claims jwt = parseJwt(token);
        final String value = getClaimsValue(jwt, ClaimKeyConstants.USER_ID);
        if (StrUtil.isBlank(value)) {
            throw new ValidationFailureException("无效 token");
        }
        return value;
    }

    /**
     * 从数据声明生成令牌
     *
     * @param claims 数据声明
     * @return 令牌
     */
    public static String createJwt(Map<String, Object> claims) {
        // 1.对秘钥做BASE64编码
        String base64 = Base64.encode(TokenConstants.SECRET.getBytes());
        // 2.生成秘钥对象,会根据base64长度自动选择相应的 HMAC 算法
        SecretKey secretKey = Keys.hmacShaKeyFor(base64.getBytes());
        return Jwts.builder()
                .setClaims(claims)
                .signWith(secretKey).compact();
    }
}
