package com.allin.view.auth.utils;

import cn.hutool.crypto.SecureUtil;
import com.allin.view.base.exception.service.ValidationFailureException;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.util.regex.Pattern;

/**
 * 密码加解密工具
 */
public class SecurityUtils {

    /**
     * des 密钥
     */
    public static final String DES_KEY = "allintech";

    /**
     * 密码正则匹配
     * 密码需由大写、小写、数字、特殊符号组成，长度为8-24个字符
     */
    public static final Pattern PASSWORD_REGEX = Pattern.compile("^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*?[~!@#$%^&*()\\-_=+./\\[\\]{}|;'\":<>]).{8,24}$");

    public static final String PASSWORD_STANDARD = "密码需由大写、小写、数字、特殊符号组成，长度为8-24个字符";


    /**
     * 生成BCryptPasswordEncoder密码
     *
     * @param password 密码
     * @return 加密字符串
     */
    public static String encryptPassword(String password) {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.encode(password);
    }

    /**
     * 判断密码是否相同
     *
     * @param rawPassword     真实密码
     * @param encodedPassword 加密后字符
     * @return 结果
     */
    public static boolean matchesPassword(String rawPassword, String encodedPassword) {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    /**
     * des解密
     * DES算法的密钥长度必须为8个字节（64位）
     */
    public static String desDecrypt(String str) {
        byte[] rawKey = DES_KEY.getBytes();
        byte[] desKey = new byte[8];
        System.arraycopy(rawKey, 0, desKey, 0, Math.min(rawKey.length, 8));
        try {
            return SecureUtil.des(desKey).decryptStr(str);
        } catch (Exception e) {
            throw new ValidationFailureException("非法的加密字符！");
        }
    }

    /**
     * des加密
     * DES算法的密钥长度必须为8个字节（64位）
     */
    public static String desEncryptBase64(String str) {
        byte[] rawKey = DES_KEY.getBytes();
        byte[] desKey = new byte[8];
        System.arraycopy(rawKey, 0, desKey, 0, Math.min(rawKey.length, 8));
        return SecureUtil.des(desKey).encryptBase64(str);
    }

    public static void main(String[] args) {
        System.out.println(desEncryptBase64("Allin@2023!"));
    }

    /**
     * 密码合法性校验
     *
     * @param isEncrypt 是否加密
     */
    public static boolean verifyPassword(String password, boolean isEncrypt) {
        if (isEncrypt) {
            final String desDecrypt = desDecrypt(password);
            return PASSWORD_REGEX.matcher(desDecrypt).matches();
        }
        return PASSWORD_REGEX.matcher(password).matches();
    }
}
