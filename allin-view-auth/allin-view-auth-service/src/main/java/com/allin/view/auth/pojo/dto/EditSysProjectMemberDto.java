package com.allin.view.auth.pojo.dto;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.io.Serializable;

import jakarta.validation.constraints.Size;

/**
 * SysProjectMember 项目成员表编辑Dto
 *
 * <AUTHOR>
 * @since 2024/03/14 11:13:08
 */
@Data
public class EditSysProjectMemberDto implements Serializable {

    /**
     * 主键
     */
    @NotBlank(message = "主键不能为空")
    @Size(max = 64, message = "主键长度不能超过64个字符")
    private String id;
    /**
     * 用户主键
     */
    @NotBlank(message = "用户主键不能为空")
    @Size(max = 64, message = "用户主键长度不能超过64个字符")
    private String userId;
    /**
     * 项目主键
     */
    @NotBlank(message = "项目主键不能为空")
    @Size(max = 64, message = "项目主键长度不能超过64个字符")
    private String projectId;
    /**
     * 备注
     */
    private String remarks;

}
