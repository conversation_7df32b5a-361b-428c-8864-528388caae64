package com.allin.view.auth.controller;

import com.allin.view.auth.api.UserPermissionApi;
import com.allin.view.auth.pojo.vo.ProjectPermissionVo;
import com.allin.view.auth.service.UserPermissionService;
import com.allin.view.base.domain.Result;
import com.allin.view.base.i18n.I18nUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户权限管理
 *
 * <AUTHOR>
 * @since 2025/7/28
 */
@Slf4j
@Validated
@RestController
public class UserPermissionController implements UserPermissionApi {

    private final UserPermissionService userPermissionService;

    public UserPermissionController(UserPermissionService userPermissionService) {
        this.userPermissionService = userPermissionService;
    }


    /**
     * 查询当前用户拥有的项目的所有权限
     */
    public Result<ProjectPermissionVo> getByProject(@RequestParam("userId") String userId,
                                                    @RequestParam("projectId") String projectId) {
        ProjectPermissionVo projectPermission = userPermissionService.getByProject(projectId, userId);
        if (projectPermission == null) {
            return Result.fail(403, I18nUtil.getMessage("auth.not.permission"));
        }
        return Result.ok(projectPermission);
    }

}
