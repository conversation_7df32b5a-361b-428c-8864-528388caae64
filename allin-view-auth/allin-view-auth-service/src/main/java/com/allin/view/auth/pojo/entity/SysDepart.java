package com.allin.view.auth.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 部门表
 *
 * <AUTHOR>
 * @TableName sys_depart
 * @since 2024/03/14 10:47:42
 */
@Data
@TableName(value = "sys_depart")
public class SysDepart implements Serializable {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 父部门(根节点的父传null值）
     */
    private String parentId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 部门编号
     */
    private String deptCode;

    /**
     * 部门类型（0：机场集团 1：分公司 2：机场 3：部门）
     */
    private Integer deptType;

    /**
     * 部门状态 1启用 0禁用
     */
    private Integer deptStatus;

    /**
     * 负责人主键
     */
    private String leaderId;

    /**
     * 部门logo
     */
    private String deptLogo;

    /**
     * 部门主标题
     */
    private String deptPrimaryTitle;

    /**
     * 部门副标题
     */
    private String deptSecondTitle;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private java.time.LocalDateTime createdTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private java.time.LocalDateTime updatedTime;

    /**
     * 删除标志(0表示存在，1表示删除)
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer deleted;

    /**
     * 租户ID
     */
    private String tenantId;

    @TableField(exist = false)
    @Serial
    private static final long serialVersionUID = 1L;

}
