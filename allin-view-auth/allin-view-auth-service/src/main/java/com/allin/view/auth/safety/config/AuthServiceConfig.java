package com.allin.view.auth.safety.config;

import com.alibaba.fastjson2.JSON;
import com.allin.view.auth.AutApiAutoConfiguration;
import com.allin.view.auth.config.properties.AuthApiProperties;
import com.allin.view.auth.interceptor.ApiKeyInterceptor;
import com.allin.view.auth.safety.controller.ApiKeyController;
import com.allin.view.auth.safety.interceptor.RequestAuthInterceptor;
import com.allin.view.auth.safety.manager.TokenManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 认证服务配置
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@AutoConfigureBefore({AutApiAutoConfiguration.class})
@EnableConfigurationProperties({AuthApiProperties.class})
public class AuthServiceConfig {

    private final AuthApiProperties authApiProperties;

    private final ApiKeyController apiKeyController;

    private final TokenManager tokenManager;

    public AuthServiceConfig(AuthApiProperties authApiProperties,
                             ApiKeyController apiKeyController,
                             TokenManager tokenManager) {
        this.authApiProperties = authApiProperties;
        this.apiKeyController = apiKeyController;
        this.tokenManager = tokenManager;
    }

    @Bean
    public RequestAuthInterceptor defaultAuthInterceptor() {
        log.info("认证属性加载成功...,{}", JSON.toJSON(authApiProperties));
        log.info("用户Token服务认证拦截器加载成功...");
        return new RequestAuthInterceptor(tokenManager, authApiProperties);
    }

    @Bean
    public ApiKeyInterceptor defaultServiceTokenAuthInterceptor() {
        log.info("第三方令牌认证拦截器加载成功...");
        return new ApiKeyInterceptor(apiKeyController);
    }

    @Bean
    public WebMvcConfigurer customWebMvcConfig(RequestAuthInterceptor requestAuthInterceptor,
                                               ApiKeyInterceptor apiKeyInterceptor) {
        return new WebMvcConfigurer() {
            @Override
            public void addInterceptors(InterceptorRegistry registry) {
                registry.addInterceptor(requestAuthInterceptor).addPathPatterns("/**");
                registry.addInterceptor(apiKeyInterceptor).addPathPatterns("/**");
            }
        };
    }
}
