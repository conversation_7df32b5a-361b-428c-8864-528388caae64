package com.allin.view.auth.service;

import com.allin.view.auth.pojo.entity.SysRoleMenu;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【sys_role_menu(角色菜单表)】的数据库操作Service
* @createDate 2024-03-14 10:55:52
*/
public interface SysRoleMenuService extends IService<SysRoleMenu> {

    void saveRoleMenu(String id, List<String> menus);

    List<String> getRoleMenu(String id);

    void deleteRoleMenu(String id);
}
