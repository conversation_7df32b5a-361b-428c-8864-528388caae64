package com.allin.view.auth;

import jakarta.annotation.PostConstruct;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.TimeZone;

@MapperScan("com.allin.view.auth.mapper")
@SpringBootApplication
@EnableCaching
@EnableScheduling
public class AllinViewAuthApplication {

    @PostConstruct
    public void system() {
        TimeZone.setDefault(TimeZone.getTimeZone("Asia/Shanghai"));
    }


    public static void main(String[] args) {
        SpringApplication.run(AllinViewAuthApplication.class, args);
    }

}
