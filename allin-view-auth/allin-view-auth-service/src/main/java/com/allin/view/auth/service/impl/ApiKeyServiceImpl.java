package com.allin.view.auth.service.impl;

import com.allin.view.auth.mapper.SysThirdPartyMapper;
import com.allin.view.auth.pojo.entity.ApiKey;
import com.allin.view.auth.service.ApiKeyService;
import com.allin.view.base.exception.service.ValidationFailureException;
import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class ApiKeyServiceImpl extends CrudRepository<SysThirdPartyMapper, ApiKey> implements ApiKeyService {

    @Override
    public ApiKey checkToken(String token, String path) {
        // 查询令牌信息
        ApiKey thirdParty = lambdaQuery().eq(ApiKey::getToken, token).one();

        if (thirdParty == null) {
            throw new ValidationFailureException("无效的令牌");
        }

        // 检查令牌是否启用
        if (thirdParty.getEnabled() != 1) {
            throw new ValidationFailureException("令牌已禁用");
        }

        // 检查令牌是否过期，没有过期时间则永久有效
        if (thirdParty.getExpireTime() != null && thirdParty.getExpireTime().isBefore(LocalDateTime.now())) {
            throw new ValidationFailureException("令牌已过期");
        }

        return thirdParty;
    }

}
