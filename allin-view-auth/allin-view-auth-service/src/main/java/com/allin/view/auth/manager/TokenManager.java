package com.allin.view.auth.manager;

import cn.hutool.core.collection.CollUtil;
import com.allin.view.auth.config.properties.AuthApiProperties;
import com.allin.view.auth.constant.CacheKeyConstants;
import com.allin.view.auth.constant.ClaimKeyConstants;
import com.allin.view.auth.pojo.dto.OnlineUser;
import com.allin.view.auth.pojo.entity.SysUser;
import com.allin.view.auth.service.SysLoginLogService;
import com.allin.view.auth.utils.TokenUtils;
import com.allin.view.base.utils.ServletUtils;
import com.allin.view.base.utils.ip.IpV4Utils;
import com.allin.view.config.redis.service.RedisService;
import com.allin.view.ws.entity.WsMessage;
import com.allin.view.ws.handler.CustomParamWebSocketHandler;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * token验证处理
 *
 * <AUTHOR>
 */
@Service
public class TokenManager {

    /**
     * PC令牌有效期（默认1天）
     */
    private static final int pcExpireTime_Day = 1;

    /**
     * APP令牌有效期（默认30天）
     */
    private static final int appExpireTime_Day = 30;

    private final RedisService redisService;

    private final AuthApiProperties authApiProperties;

    private final CustomParamWebSocketHandler webSocketHandler;

    private final SysLoginLogService sysLoginLogService;

    public TokenManager(RedisService redisService,
                        AuthApiProperties authApiProperties,
                        CustomParamWebSocketHandler webSocketHandler,
                        SysLoginLogService sysLoginLogService) {
        this.redisService = redisService;
        this.authApiProperties = authApiProperties;
        this.webSocketHandler = webSocketHandler;
        this.sysLoginLogService = sysLoginLogService;
    }


    /**
     * 创建令牌
     *
     * @param sysUser 用户信息
     * @param isApp   是否app登录
     * @return {@link String}
     * <AUTHOR>
     */
    public OnlineUser createToken(SysUser sysUser, boolean isApp) {
        String userCacheKey = TokenUtils.generateUserCacheKey(sysUser.getId());
        OnlineUser onlineUser = new OnlineUser();
        // Jwt存储信息
        Map<String, Object> claimsMap = new HashMap<>();
        claimsMap.put(ClaimKeyConstants.USER_CACHE_KEY, userCacheKey);
        claimsMap.put(ClaimKeyConstants.USER_ID, sysUser.getId());
        onlineUser.setToken(TokenUtils.createJwt(claimsMap));
        onlineUser.setApp(isApp);
        onlineUser.setLoginIp(IpV4Utils.getIpAddr(ServletUtils.getRequest()));
        if (isApp) {
            refreshTokenCache(userCacheKey, onlineUser, appExpireTime_Day);
        } else {
            refreshTokenCache(userCacheKey, onlineUser, pcExpireTime_Day);
        }
        if (!authApiProperties.isAllowMultipleLogin()) {
            // 强制下线其他
            forceLogoutOther(sysUser.getId(), userCacheKey, isApp);
        }
        return onlineUser;
    }

    /**
     * 同端只允许一个登录，强制下线其他
     */
    private void forceLogoutOther(String userId, String userCacheKey, boolean isApp) {
        final Set<String> userCacheKeys = redisService.scanKeys(RedisService.generateKey(CacheKeyConstants.LOGIN_CACHE_KEY, userId, "*"));
        if (CollUtil.isNotEmpty(userCacheKeys)) {
            userCacheKeys.stream().filter(e -> !e.equals(userCacheKey)).forEach(e -> {
                final OnlineUser onlineUser = redisService.getCacheObject(e);
                // 删除掉同端的其他登录
                if (onlineUser != null && onlineUser.isApp() == isApp) {
                    redisService.deleteObject(e);
                }
                webSocketHandler.getSender().sendToParam(userId, WsMessage.of("logout", "您的账号在其他设备登录，您被迫下线！"));
            });
        }
    }

    /**
     * 续费令牌有效期，当只剩下1/2时，自动刷新缓存
     * 请在调用refreshToken之前先通过{@link TokenUtils#isNeedToRefresh(OnlineUser)}该方法验证是否需要续费
     *
     * @param onlineUser 在线用户
     * @see TokenUtils#isNeedToRefresh(OnlineUser)
     */
    public void refreshToken(String userCacheKey, OnlineUser onlineUser) {
        if (onlineUser.isApp()) {
            refreshTokenCache(userCacheKey, onlineUser, appExpireTime_Day);
        } else {
            refreshTokenCache(userCacheKey, onlineUser, pcExpireTime_Day);
        }
    }

    /**
     * 刷新令牌的缓存和有效期
     *
     * @param userCacheKey  redis key
     * @param expireTimeDay 过期时间, 单位天
     */
    public void refreshTokenCache(String userCacheKey, OnlineUser onlineUser, Integer expireTimeDay) {
        final LocalDateTime now = LocalDateTime.now();
        onlineUser.setLoginTime(now);
        onlineUser.setExpireTime(now.plusDays(expireTimeDay));
        redisService.setCacheObject(userCacheKey, onlineUser, expireTimeDay, TimeUnit.DAYS);
    }

    /**
     * 注销token
     */
    public void logout(String token) {
        final String userCacheKey = TokenUtils.getUserCacheKey(token);
        final String userId = TokenUtils.getUserId(token);
        OnlineUser onlineUser = redisService.getCacheObject(userCacheKey);
        if (onlineUser != null) {
            // 删除用户缓存记录
            redisService.deleteObject(userCacheKey);
            // 记录用户退出日志
            sysLoginLogService.recordSuccessLog(userId, "退出登录");
        }
    }
}