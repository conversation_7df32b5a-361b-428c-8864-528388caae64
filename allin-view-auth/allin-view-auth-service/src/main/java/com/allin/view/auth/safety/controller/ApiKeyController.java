package com.allin.view.auth.safety.controller;

import com.allin.view.auth.api.ApiKeyApi;
import com.allin.view.auth.interceptor.annotation.WhiteListApi;
import com.allin.view.auth.pojo.dto.ApiKeyCheckDto;
import com.allin.view.auth.pojo.entity.ApiKey;
import com.allin.view.auth.pojo.vo.ApiKeyVo;
import com.allin.view.auth.service.ApiKeyService;
import com.allin.view.base.domain.Result;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;

/**
 * ApiKey控制器
 *
 * <AUTHOR>
 * @date 2024/5/30
 */
@Slf4j
@RestController
public class ApiKeyController implements ApiKeyApi {

    private final ApiKeyService apiKeyService;

    public ApiKeyController(ApiKeyService apiKeyService) {
        this.apiKeyService = apiKeyService;
    }

    @WhiteListApi
    @Override
    public Result<ApiKeyVo> check(ApiKeyCheckDto checkDto) {

        // 查询令牌信息
        ApiKey apiKey = apiKeyService.getOne(Wrappers.lambdaQuery(ApiKey.class)
                .eq(ApiKey::getToken, checkDto.getToken()));

        if (apiKey == null) {
            return Result.fail("无效的令牌");
        }

        // 检查令牌是否启用
        if (apiKey.getEnabled() != 1) {
            return Result.fail("令牌已禁用");
        }

        // 检查令牌是否过期，没有过期时间则永久有效
        if (apiKey.getExpireTime() != null && apiKey.getExpireTime().isBefore(LocalDateTime.now())) {
            return Result.fail("令牌已过期");
        }

        // 检查是否有权限
        final ApiKeyVo vo = new ApiKeyVo();
        BeanUtils.copyProperties(apiKey, vo);
        return Result.ok(vo);
    }
}