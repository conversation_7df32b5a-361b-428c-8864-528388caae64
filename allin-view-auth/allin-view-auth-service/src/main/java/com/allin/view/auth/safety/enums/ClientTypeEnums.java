package com.allin.view.auth.safety.enums;

import com.allin.view.base.enums.base.IEnums;
import lombok.Getter;

/**
 * 客户端类型, PC, APP
 */
public enum ClientTypeEnums implements IEnums {

    /**
     * 1 天过期
     */
    PC("PC", 1),

    /**
     * 30 天过期
     */
    APP("APP", 30);

    private final String code;

    /**
     * refreshToken 过期时间
     */
    @Getter
    private final Integer expireDay;

    ClientTypeEnums(String code, Integer expireDay) {
        this.code = code;
        this.expireDay = expireDay;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return code;
    }
}
