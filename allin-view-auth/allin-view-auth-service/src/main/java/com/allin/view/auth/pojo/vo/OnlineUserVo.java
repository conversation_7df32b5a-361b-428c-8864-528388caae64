package com.allin.view.auth.pojo.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户在线信息
 * <AUTHOR>
 * @date 2024/2/28
 */
@Data
public class OnlineUserVo {

    /**
     * 雪花id, 用户唯一标识
     */
    private String id;

    /**
     * 账号(用户名)
     */
    private String userName;

    /**
     * 姓名
     */
    private String fullName;

    /**
     * 头像
     */
    private String headImg;
    
    /**
     * 登录时间
     */
    private LocalDateTime loginTime;

    /**
     * 过期时间
     */
    private LocalDateTime expireTime;

    /**
     * 登录ip
     */
    private String loginIp;

    /**
     * 客户端类型
     */
    private String clientType;
}
