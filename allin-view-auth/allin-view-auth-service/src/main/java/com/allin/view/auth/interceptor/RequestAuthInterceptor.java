package com.allin.view.auth.interceptor;

import cn.hutool.core.util.StrUtil;
import com.allin.view.auth.config.properties.AuthApiProperties;
import com.allin.view.auth.constant.SecurityContextConstants;
import com.allin.view.auth.context.SecurityContextHolder;
import com.allin.view.auth.controller.TokenController;
import com.allin.view.auth.pojo.dto.TokenDto;
import com.allin.view.auth.pojo.vo.LoginUser;
import com.allin.view.auth.utils.InterceptorUtils;
import com.allin.view.auth.utils.TokenValidateUtils;
import com.allin.view.base.domain.Result;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import java.io.IOException;

/**
 * 请求认证拦截器，会校验token并将用户信息数据封装到线程变量中方便获取
 *
 * <AUTHOR>
 */
public class RequestAuthInterceptor implements HandlerInterceptor {

    private final TokenController tokenController;

    private final AuthApiProperties authApiProperties;

    public RequestAuthInterceptor(TokenController tokenController,
                                  AuthApiProperties authApiProperties) {
        this.tokenController = tokenController;
        this.authApiProperties = authApiProperties;
    }

    /**
     * 验证Token的有效性
     * 验证通过后会将用户信息存入SecurityContextHolder
     *
     * @param request  HTTP请求对象
     * @param response HTTP响应对象
     * @return 如果Token验证通过返回true，否则返回false
     * @throws IOException 写入响应时可能发生IO异常
     */
    private boolean validateToken(HttpServletRequest request, HttpServletResponse response) throws IOException {
        final String token = TokenValidateUtils.getToken(request);
        if (StrUtil.isBlank(token)) {
            InterceptorUtils.writerResponse(response, "用户未登录！");
            return false;
        }

        final Result<LoginUser> check = tokenController.check(new TokenDto(token));
        if (check.isOk()) {
            SecurityContextHolder.set(SecurityContextConstants.USER_INFO, check.getData());
            return true;
        } else {
            InterceptorUtils.writerResponse(response, check.getMessage());
            return false;
        }
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
                                Exception ex) {
        SecurityContextHolder.remove();
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws IOException {
        // 如果不是映射到方法，直接通过
        if (!(handler instanceof HandlerMethod requestMethod)) {
            return true;
        }

        // OPTIONS请求直接放行
        if (InterceptorUtils.isOptionsRequest(request)) {
            response.setStatus(HttpServletResponse.SC_OK);
            return true;
        }

        // 第三方令牌校验已通过不执行用户token校验
        if (SecurityContextHolder.exist(SecurityContextConstants.THIRD_PARTY_INFO)) {
            return true;
        }

        // 如果是心跳请求，直接通过
        if (request.getRequestURI().contains("/heartbeat")) {
            return true;
        }

        // 服务内部请求直接跳过后续校验
        if (InterceptorUtils.isInternalService(request)) {
            return true;
        }

        // IP白名单校验
        if (!InterceptorUtils.isIpInWhitelist(response, authApiProperties.getIpWhites())) {
            return false;
        }

        // 设置项目ID到上下文
        SecurityContextHolder.setProjectId(request.getHeader(SecurityContextConstants.PROJECT_ID));

        // 白名单请求校验
        if (InterceptorUtils.isWhiteListRequest(request, requestMethod, authApiProperties.getApiWhites())) {
            return true;
        }

        // Token校验
        return validateToken(request, response);
    }
}
