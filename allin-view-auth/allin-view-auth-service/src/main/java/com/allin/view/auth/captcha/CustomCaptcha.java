package com.allin.view.auth.captcha;

import io.springboot.captcha.base.Captcha;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Random;

/**
 * 自定义验证码图片
 *
 * <AUTHOR>
 * @since 2025/5/13
 */
@Slf4j
public class CustomCaptcha extends Captcha {

    private static final Random random = new Random();

    public CustomCaptcha() {
    }

    public CustomCaptcha(int width, int height) {
        this();
        this.setWidth(width);
        this.setHeight(height);
    }

    public CustomCaptcha(int width, int height, int len) {
        this(width, height);
        this.setLen(len);
    }

    public CustomCaptcha(int width, int height, int len, Font font) {
        this(width, height, len);
        this.setFont(font);
    }

    public boolean out(OutputStream out) {
        return this.graphicsImage(this.textChar(), out);
    }

    public String toBase64() {
        return this.toBase64("data:image/png;base64,");
    }

    public String getContentType() {
        return "image/png";
    }

    /**
     * 随机生产红黄绿白四种颜色
     */
    private Color drawOval() {
        // 生成一个随机数，代表颜色的索引
        int colorIndex = random.nextInt(2);

        // 根据索引返回相应的颜色
        return switch (colorIndex) {
            case 0 -> Color.YELLOW;
            case 1 -> Color.GREEN;
            default ->
                // 如果出现意外情况，返回黑色
                    Color.BLACK;
        };
    }

    private boolean graphicsImage(char[] strs, OutputStream out) {
        BufferedImage bi = null;

        try {
            // 修改为支持透明背景的图像类型
            bi = new BufferedImage(this.width, this.height, BufferedImage.TYPE_INT_ARGB);
            Graphics2D g2d = (Graphics2D) bi.getGraphics();

            // 启用抗锯齿
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

            // 绘制干扰线和椭圆
            this.drawOval(2, g2d);
            g2d.setStroke(new BasicStroke(2.0F, 0, 2));
            this.drawBesselLine(1, g2d);

            // 设置字体并绘制文字
            g2d.setFont(this.getFont());
            FontMetrics fontMetrics = g2d.getFontMetrics();
            int fW = this.width / strs.length;
            int fSp = (fW - (int) fontMetrics.getStringBounds("W", g2d).getWidth()) / 2;

            for (int i = 0; i < strs.length; ++i) {
                g2d.setColor(drawOval());
                int fY = this.height - (this.height - (int) fontMetrics.getStringBounds(String.valueOf(strs[i]), g2d).getHeight() >> 1);
                g2d.drawString(String.valueOf(strs[i]), i * fW + fSp + 3, fY - 3);
            }

            g2d.dispose();
            ImageIO.write(bi, "png", out);
            out.flush();
            return true;
        } catch (IOException var18) {
            log.error(var18.getMessage(), var18);
        } finally {
            try {
                out.close();
            } catch (IOException var17) {
                log.error(var17.getMessage(), var17);
            }

            if (bi != null) {
                bi.getGraphics().dispose();
            }
        }

        return false;
    }
}
