package com.allin.view.auth.safety.controller;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.allin.view.auth.interceptor.annotation.WhiteListApi;
import com.allin.view.auth.pojo.dto.ImageCaptchaCheckDto;
import com.allin.view.auth.pojo.vo.ImageCaptcha;
import com.allin.view.auth.safety.captcha.CustomCaptcha;
import com.allin.view.auth.safety.constant.CacheKeyConstants;
import com.allin.view.base.domain.Result;
import com.allin.view.base.i18n.I18nUtil;
import com.allin.view.config.cache.CacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.TimeUnit;

/**
 * 认证授权/验证码
 *
 * <AUTHOR>
 * @date 2024/2/29
 */
@Slf4j
@Validated
@WhiteListApi
@RestController
@RequestMapping("/captcha")
public class CaptchaController {

    private final CacheService redisService;

    public CaptchaController(CacheService redisService) {
        this.redisService = redisService;
    }

    /**
     * 生成图形验证码
     */
    @GetMapping("/generate/image")
    public Result<ImageCaptcha> generateImageCaptcha() {
        CustomCaptcha specCaptcha = new CustomCaptcha(130, 48, 5);
        String verCode = specCaptcha.text().toLowerCase();
        String captchaId = IdUtil.fastSimpleUUID();
        String verifyKey = CacheKeyConstants.CAPTCHA_VERIFY_KEY.apply(captchaId);
        // 存入 redis 并设置过期时间为3分钟
        redisService.setCacheObject(verifyKey, verCode, 3L, TimeUnit.MINUTES);
        // 将key 和 base64 返回给前端
        final ImageCaptcha imageCaptcha = new ImageCaptcha();
        imageCaptcha.setId(captchaId);
        imageCaptcha.setImg(specCaptcha.toBase64());
        return Result.ok(imageCaptcha);
    }

    /**
     * 校验图形验证码
     */
    @PostMapping("/check/image")
    public Result<String> checkImageCaptcha(@Validated @RequestBody ImageCaptchaCheckDto checkDto) {
        // 生成 Redis 验证码键
        String captchaVerifyKey = CacheKeyConstants.CAPTCHA_VERIFY_KEY.apply(checkDto.getId());
        // 获取 Redis 中的验证码
        String redisCode = redisService.getCacheObject(captchaVerifyKey);
        if (StrUtil.isBlank(redisCode)) {
            return Result.fail(I18nUtil.getMessage("auth.captcha.expire"));
        }
        // 检查用户输入的验证码
        String verCode = checkDto.getVerCode().trim();
        if (StrUtil.isBlank(verCode) || !verCode.equalsIgnoreCase(redisCode)) {
            return Result.fail(I18nUtil.getMessage("auth.captcha.error"));
        }
        // 删除 Redis 中的验证码
        redisService.deleteObject(captchaVerifyKey);

        // 生成成功标记键并存入 Redis
        String succKey = CacheKeyConstants.CAPTCHA_SUCC_KEY.apply(checkDto.getId());
        // 存入 redis 并设置过期时间为3分钟
        redisService.setCacheObject(succKey, "true", 3L, TimeUnit.MINUTES);
        return Result.ok("验证通过");
    }
}



