package com.allin.view.auth.safety.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.allin.view.auth.config.properties.AuthApiProperties;
import com.allin.view.auth.constant.WebSocketTypeConstants;
import com.allin.view.auth.pojo.dto.LoginBody;
import com.allin.view.auth.pojo.entity.SysUser;
import com.allin.view.auth.safety.constant.CacheKeyConstants;
import com.allin.view.auth.safety.enums.ClientTypeEnums;
import com.allin.view.auth.safety.pojo.dto.JwtToken;
import com.allin.view.auth.safety.pojo.dto.OnlineUser;
import com.allin.view.auth.service.SysLoginLogService;
import com.allin.view.auth.service.SysUserService;
import com.allin.view.base.enums.base.IEnums;
import com.allin.view.base.exception.service.ValidationFailureException;
import com.allin.view.base.utils.ServletUtils;
import com.allin.view.base.utils.ip.IpV4Utils;
import com.allin.view.config.cache.CacheService;
import com.allin.view.ws.entity.WsMessage;
import com.allin.view.ws.handler.CustomParamWebSocketHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * token验证处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class TokenManager {

    private final CacheService cacheService;

    private final AuthApiProperties authApiProperties;

    private final CustomParamWebSocketHandler userWebSocketHandler;

    private final SysLoginLogService sysLoginLogService;

    private final SysUserService userService;

    public TokenManager(CacheService cacheService,
                        AuthApiProperties authApiProperties,
                        CustomParamWebSocketHandler userWebSocketHandler,
                        SysLoginLogService sysLoginLogService,
                        SysUserService userService) {
        this.cacheService = cacheService;
        this.authApiProperties = authApiProperties;
        this.userWebSocketHandler = userWebSocketHandler;
        this.sysLoginLogService = sysLoginLogService;
        this.userService = userService;
    }

    /**
     * 同端只允许一个登录，强制下线其他
     */
    public void forceLogoutOther(SysUser user, LoginBody loginBody) {
        if (authApiProperties.isAllowMultipleLogin()) {
            return;
        }
        final String cacheKeyPattern = CacheKeyConstants.getTokenCacheKeyPattern(user.getId());
        final Set<String> scanKeys = cacheService.scanKeys(cacheKeyPattern);
        if (CollUtil.isEmpty(scanKeys)) {
            return;
        }
        scanKeys.forEach(key -> {
            final OnlineUser onlineUser = cacheService.getCacheObject(key);
            // 删除掉同端的其他登录
            if (onlineUser != null && Objects.equals(loginBody.getClientType(), onlineUser.getClientType())) {
                cacheService.deleteObject(key);
            }
            userWebSocketHandler.getSender().sendToParam(user.getId(),
                    WsMessage.of(WebSocketTypeConstants.LOGOUT, "您的账号在其他设备登录！"));
        });
    }

    /**
     * 根据未过期的旧token返回新token
     */
    public synchronized String doRefreshToken(String token) {
        // 校验 token
        JwtToken.verify(token);

        // 解析 token
        final JwtToken oldJwtToken = JwtToken.parse(token);

        // 校验 refreshId 是否已经过期
        final String tokenCacheKey = CacheKeyConstants.getTokenCacheKey(oldJwtToken.getUser_id(), oldJwtToken.getRefresh_id());

        if (cacheService.getCacheObject(tokenCacheKey) == null) {
            throw new ValidationFailureException(401, StrUtil.format("refreshId {} 已过期, 请重新登录", oldJwtToken.getRefresh_id()));
        }

        SysUser user = userService.getByUserId(String.valueOf(oldJwtToken.getUser_id()));

        // 用户合法性校验
        userService.userVerification(user);

        // 创建 jwtToken
        final JwtToken newJwtToken = new JwtToken(user, oldJwtToken.getJti());

        // 刷新 token 过期时间
        refreshOnlineUser(oldJwtToken, newJwtToken);

        return newJwtToken.toStr();
    }

    /**
     * 注销token
     */
    public void logout(String token) {
        JwtToken jwtToken = JwtToken.parse(token);
        final String tokenCacheKey = CacheKeyConstants.getTokenCacheKey(jwtToken.getUser_id(), jwtToken.getRefresh_id());
        OnlineUser onlineUser = cacheService.getCacheObject(tokenCacheKey);
        if (onlineUser != null) {
            // 删除用户缓存记录
            cacheService.deleteObject(tokenCacheKey);
            // 记录用户退出日志
            sysLoginLogService.recordSuccessLog(jwtToken.getUser_id(), "退出登录");
        }
    }

    /**
     * 所有在线用户信息
     */
    public List<OnlineUser> listOnlineUser() {
        return cacheService.listCacheObject(CacheKeyConstants.getTokenCacheKeyPattern());
    }

    /**
     * 记录在线用户信息
     */
    public void refreshOnlineUser(JwtToken oldJwtToken, JwtToken newJwtToken) {
        final ClientTypeEnums clientType = newJwtToken.clientTypeEnum();

        OnlineUser onlineUser = new OnlineUser();
        onlineUser.setUserId(newJwtToken.getUser_id());
        onlineUser.setLoginTime(LocalDateTimeUtil.of(oldJwtToken.getIat()));
        onlineUser.setExpireTime(LocalDateTime.now().plusDays(clientType.getExpireDay()));
        onlineUser.setClientType(newJwtToken.getClient_type());
        onlineUser.setLoginIp(IpV4Utils.getIpAddr(ServletUtils.getRequest()));

        // 设置缓存内容和时长
        cacheService.setCacheObject(CacheKeyConstants.getTokenCacheKey(onlineUser.getUserId(), newJwtToken.getRefresh_id()),
                onlineUser, clientType.getExpireDay(), TimeUnit.DAYS);
    }

    /**
     * 记录在线用户信息
     */
    public void setOnlineUser(JwtToken jwtToken) {
        final ClientTypeEnums clientType = IEnums.tryFindByCode(ClientTypeEnums.class, jwtToken.getClient_type())
                .orElseThrow(() -> new ValidationFailureException("客户端类型不合法"));

        final LocalDateTime now = LocalDateTime.now();

        OnlineUser onlineUser = new OnlineUser();
        onlineUser.setUserId(jwtToken.getUser_id());
        onlineUser.setLoginTime(now);
        onlineUser.setExpireTime(now.plusDays(clientType.getExpireDay()));
        onlineUser.setClientType(jwtToken.getClient_type());
        onlineUser.setLoginIp(IpV4Utils.getIpAddr(ServletUtils.getRequest()));

        // 设置缓存内容和时长
        cacheService.setCacheObject(CacheKeyConstants.getTokenCacheKey(onlineUser.getUserId(), jwtToken.getRefresh_id()),
                onlineUser, clientType.getExpireDay(), TimeUnit.DAYS);
    }
}