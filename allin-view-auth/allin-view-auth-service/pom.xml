<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.allin</groupId>
        <artifactId>allin-view-auth</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>allin-view-auth-service</artifactId>
    <version>${revision}</version>
    <name>allin-view-auth-service</name>
    <description>认证中心服务</description>
    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.deploy.skip>true</maven.deploy.skip>
        <maven.install.skip>true</maven.install.skip>
        <captcha.version>2.2.5</captcha.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-crypto</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-3-starter</artifactId>
        </dependency>

        <!-- 验证码 start-->
        <dependency>
            <groupId>io.springboot.plugin</groupId>
            <artifactId>captcha-spring-boot-starter</artifactId>
            <version>${captcha.version}</version>
        </dependency>
        <!-- 验证码 end-->
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.allin</groupId>
            <artifactId>allin-view-auth-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.allin</groupId>
            <artifactId>allin-view-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.allin</groupId>
            <artifactId>allin-view-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.allin</groupId>
            <artifactId>allin-view-ws</artifactId>
        </dependency>
        <dependency>
            <groupId>com.allin</groupId>
            <artifactId>allin-view-log-component</artifactId>
        </dependency>
        <dependency>
            <groupId>com.allin</groupId>
            <artifactId>allin-view-dict-component</artifactId>
        </dependency>
        <dependency>
            <groupId>com.allin</groupId>
            <artifactId>allin-view-file-component</artifactId>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
