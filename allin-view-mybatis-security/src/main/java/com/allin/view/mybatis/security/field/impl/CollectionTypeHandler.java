package com.allin.view.mybatis.security.field.impl;

import com.allin.view.mybatis.security.field.ProcessContext;
import com.allin.view.mybatis.security.field.TypeHandler;
import com.allin.view.mybatis.security.util.MaskAnnotationResolver;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Deque;
import java.util.Set;

/**
 * 处理Collection数据类型
 *
 * <AUTHOR>
 * @date 2023/9/6 17:00
 */
public class CollectionTypeHandler implements TypeHandler {

    @Override
    public boolean handle(ProcessContext processContext) {
        Object fieldValue = processContext.getFieldValue();
        Set<Integer> referenceSet = processContext.getReferenceSet();
        Deque<Object> analyzeDeque = processContext.getAnalyzeDeque();
        if (fieldValue instanceof Collection<?> fieldValueList) {
            if (CollectionUtils.isEmpty(fieldValueList)) {
                return false;
            }
            for (Object collectionObject : fieldValueList) {
                if (MaskAnnotationResolver.isNotBaseType(collectionObject.getClass(), collectionObject, referenceSet)) {
                    analyzeDeque.offer(collectionObject);
                }
            }
            return true;
        }
        return false;
    }
}
