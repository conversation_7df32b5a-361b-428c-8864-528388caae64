CREATE TABLE "file_info" (
                             "id" varchar(19) NOT NULL,
                             "file_name" varchar(255) NOT NULL,
                             "file_url" varchar(255) NOT NULL,
                             "upload_type" varchar(20),
                             "file_type" varchar(255),
                             "file_size" int8,
                             "md5" varchar(255),
                             "created_time" timestamp NOT NULL,
                             PRIMARY KEY ("id")
);
CREATE INDEX "idx_file_name" ON "file_info" USING btree (
    "file_name" ASC
    );
CREATE INDEX "idx_md5" ON "file_info" USING btree (
    "md5" ASC
    );
CREATE INDEX "idx_url" ON "file_info" USING btree (
    "file_url" ASC
    );
COMMENT ON COLUMN "file_info"."id" IS '主键id';
COMMENT ON COLUMN "file_info"."file_name" IS '文件名称';
COMMENT ON COLUMN "file_info"."file_url" IS '文件url';
COMMENT ON COLUMN "file_info"."upload_type" IS '上传类型';
COMMENT ON COLUMN "file_info"."file_type" IS '文件类型';
COMMENT ON COLUMN "file_info"."file_size" IS '文件大小';
COMMENT ON COLUMN "file_info"."md5" IS '文件md5';
COMMENT ON COLUMN "file_info"."created_time" IS '创建时间';
COMMENT ON TABLE "file_info" IS '文件信息表';