package com.allin.view.file;

import com.allin.view.file.properties.FileProperties;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;

/**
 * 基础文件功能
 * <AUTHOR>
 * @date 2023/2/17
 */
@Slf4j
@AutoConfiguration
@EnableConfigurationProperties(FileProperties.class)
@ComponentScan("com.allin.view.file")
@MapperScan("com.allin.view.file.mapper")
public class FileAutoConfiguration {

    public FileAutoConfiguration() {
        log.info("文件组件加载成功...");
    }
}
