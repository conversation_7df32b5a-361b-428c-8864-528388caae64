package com.allin.view.file.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.allin.view.base.exception.service.ProgramException;
import com.allin.view.base.exception.service.ValidationFailureException;
import com.allin.view.base.execl.EasyExcelOperUtils;
import com.allin.view.file.pojo.entity.FileInfo;
import com.allin.view.file.pojo.vo.FileVo;
import com.allin.view.file.service.FileService;
import org.springframework.beans.BeanUtils;
import org.springframework.mock.web.MockMultipartFile;

import java.io.File;
import java.util.List;
import java.util.Objects;

/**
 * 用来给其他服务使用的上传工具类
 *
 * <AUTHOR>
 * @date 2023/9/28
 */
public class FileUploadUtils {

    /**
     * 错误数据生成excel并上传到文件服务
     * 参考：
     * <pre>{@code
     *         final DrillPlanListener importListener = new DrillPlanListener();
     *         EasyExcel.read(file.getInputStream(), ImportDrillPlanDto.class, importListener).sheet().doRead();
     *         final ImportResultVo importResultVo = new ImportResultVo();
     *         importResultVo.setSuccTotal(importListener.getSuccTotal());
     *         importResultVo.setErrorTotal((long) importListener.getErrorDataList().size());
     *         if (CollUtil.isNotEmpty(importListener.getErrorDataList())) {
     *             final List<ErrorImportDrillPlanDto> errorDataList = importListener.getErrorDataList();
     *             final FileVo fileVo = FileUploadUtils.createErrorExcelAndUpload(errorDataList);
     *             importResultVo.setErrorFileUrl(fileVo.getUrl());
     *         }
     *         return Result.ok(importResultVo);
     * }</pre>
     *
     * @param errorDataList 错误数据
     */
    public static <T> FileVo createErrorExcelAndUpload(List<T> errorDataList) {
        if (errorDataList != null && !errorDataList.isEmpty()) {
            // 从集合中获取 Class
            Class<T> execlDataClass = (Class<T>) errorDataList.get(0).getClass();
            final File file = EasyExcelOperUtils.writeToLocalPath(errorDataList, execlDataClass, "error");
            final FileService fileService = SpringUtil.getBean(FileService.class);
            final FileInfo fileInfo;
            try {
                fileInfo = fileService.uploadFile(new MockMultipartFile(
                        file.getName(),
                        file.getName(),
                        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                        FileUtil.readBytes(file)));
            } catch (Exception e) {
                throw new ProgramException("上传文件失败! ");
            }
            FileUtil.del(file);
            if (Objects.isNull(fileInfo)) {
                throw new ProgramException("上传文件失败! ");
            }
            FileVo fileVo = new FileVo();
            BeanUtils.copyProperties(fileInfo, fileVo);
            return fileVo;
        }
        throw new ValidationFailureException("错误数据不能为空! ");
    }
}