package com.allin.view.tcp.server;

import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelOutboundHandlerAdapter;
import io.netty.channel.ChannelPromise;

/**
 * <AUTHOR>
 */
public class TcpOutHandler extends ChannelOutboundHandlerAdapter {

    @Override
    public void write(ChannelHandlerContext ctx, Object msg, ChannelPromise promise) throws Exception {
        if (msg instanceof byte[] bytesWrite) {
            ByteBuf buf = ctx.alloc().buffer(bytesWrite.length);
            buf.writeBytes(bytesWrite);
            ctx.writeAndFlush(buf);
        }
    }
}
